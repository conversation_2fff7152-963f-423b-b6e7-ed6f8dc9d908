# Thread Final Grading API

## Overview

This document describes the new endpoint for grading final thread registrations and automatically creating transcript entries.

## Endpoint

### Grade Final Thread Registration

**POST** `/thread/grade-final`

Grades a final thread registration and automatically creates a transcript entry.

#### Request Body

```json
{
   "user_id": 1,
   "thread_id": 2,
   "final_grade": 85.5
}
```

#### Request Parameters

-  `user_id` (integer, required): ID of the student to grade
-  `thread_id` (integer, required): ID of the thread
-  `final_grade` (float, required): Final grade between 0 and 100

#### Response

```json
{
   "message": "Final grade updated successfully",
   "registration": {
      "user_id": 1,
      "thread_id": 2,
      "final_grade": 85.5
   }
}
```

#### Error Responses

-  `400 Bad Request`: Invalid request body, validation errors, or user has no transcript
-  `404 Not Found`: User is not registered for this thread
-  `500 Internal Server Error`: Server error

## Transcript Validation

Before processing the final grade, the system validates that:

1. **User has a transcript**: The system checks if the user has an academic transcript in the database
2. **Transcript accessibility**: If no transcript is found, the request fails with a 400 Bad Request error

## Automatic Transcript Entry Creation

When a final grade is successfully recorded, the system automatically:

1. **Updates the thread registration** with the final grade
2. **Creates a transcript entry** with the following details:
   -  Uses the user's existing transcript ID
   -  Grade letter (converted from numeric grade)
   -  Grade points (converted from numeric grade)
   -  Credits: Always set to 5
   -  Course and semester information from the thread
   -  Completion date: Current timestamp

### Grade Conversion

The system uses the following conversion tables:

#### Numeric to Letter Grade

-  95-100: A+
-  90-94: A
-  85-89: A-
-  80-84: B+
-  75-79: B
-  70-74: B-
-  65-69: C+
-  60-64: C
-  55-59: C-
-  50-54: D
-  0-49: F

#### Numeric to Grade Points (4.0 scale)

-  95-100: 4.0
-  90-94: 3.7
-  85-89: 3.3
-  80-84: 3.0
-  75-79: 2.7
-  70-74: 2.3
-  65-69: 2.0
-  60-64: 1.7
-  55-59: 1.3
-  50-54: 1.0
-  0-49: 0.0

## Example Usage

### Curl Example

```bash
curl -X POST http://localhost:8081/thread/grade-final \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "user_id": 1,
    "thread_id": 2,
    "final_grade": 85.5
  }'
```

### Expected Response

```json
{
   "message": "Final grade updated successfully",
   "registration": {
      "user_id": 1,
      "thread_id": 2,
      "final_grade": 85.5
   }
}
```

### Error Example - User Has No Transcript

```bash
curl -X POST http://localhost:8081/thread/grade-final \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "user_id": 999,
    "thread_id": 2,
    "final_grade": 85.5
  }'
```

**Response (400 Bad Request):**

```json
{
   "error": "User has no transcript"
}
```

## Notes

-  **Transcript validation**: The system validates that the user has a transcript before processing the grade
-  The transcript entry creation happens asynchronously in the background
-  If transcript entry creation fails, the final grade is still recorded
-  Errors in transcript creation are logged but don't affect the main response
-  The system always sets credits to 5 for transcript entries as per requirements
-  The system automatically finds and uses the user's existing transcript ID
