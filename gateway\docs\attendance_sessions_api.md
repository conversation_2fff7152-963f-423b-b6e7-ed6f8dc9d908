# Attendance Sessions API

## Overview
This document describes the new endpoint for retrieving all scheduled sessions for a thread where attendance can be marked.

## Endpoint

### List Attendance Sessions for Thread
**GET** `/thread/{thread_id}/attendance-sessions`

Returns all scheduled class sessions for a thread based on the thread's schedule and semester dates.

#### URL Parameters
- `thread_id` (integer, required): ID of the thread

#### Response
```json
{
  "sessions": [
    {
      "date": "2024-09-16",
      "day_of_week": 1,
      "start_time": "09:00:00",
      "end_time": "10:30:00",
      "location": "Room 101",
      "has_attendance": false
    },
    {
      "date": "2024-09-23",
      "day_of_week": 1,
      "start_time": "09:00:00",
      "end_time": "10:30:00",
      "location": "Room 101",
      "has_attendance": false
    }
  ]
}
```

#### Response Fields
- `date` (string): Date of the session in YYYY-MM-DD format
- `day_of_week` (integer): Day of week (1=Monday, 2=Tuesday, ..., 7=Sunday)
- `start_time` (string): Start time in HH:MM:SS format
- `end_time` (string): End time in HH:MM:SS format
- `location` (string): Location/room name for the session
- `has_attendance` (boolean): Whether attendance has been marked for this session (currently always false)

#### Error Responses
- `400 Bad Request`: Invalid thread ID
- `404 Not Found`: Thread not found
- `500 Internal Server Error`: Server error

## How It Works

1. **Thread Lookup**: Retrieves thread information to get the associated semester
2. **Semester Dates**: Gets the semester start and end dates
3. **Schedule Matching**: Retrieves the thread's weekly schedule template
4. **Session Generation**: Generates all class sessions by:
   - Iterating through each day in the semester
   - Matching days with the thread's schedule (e.g., if thread has classes on Mondays)
   - Creating a session entry for each matching day

## Example Usage

### Get all attendance sessions for thread ID 3
```bash
curl -X GET http://localhost:8081/thread/3/attendance-sessions \
  -H "Authorization: Bearer <token>"
```

### Expected Response
```json
{
  "sessions": [
    {
      "date": "2024-09-02",
      "day_of_week": 1,
      "start_time": "09:00:00",
      "end_time": "10:30:00",
      "location": "Room 101",
      "has_attendance": false
    },
    {
      "date": "2024-09-09",
      "day_of_week": 1,
      "start_time": "09:00:00",
      "end_time": "10:30:00",
      "location": "Room 101",
      "has_attendance": false
    },
    {
      "date": "2024-09-16",
      "day_of_week": 1,
      "start_time": "09:00:00",
      "end_time": "10:30:00",
      "location": "Room 101",
      "has_attendance": false
    }
  ]
}
```

## Use Cases

This endpoint is perfect for:

1. **Teacher Dashboard**: Display a calendar view of all class sessions where attendance can be marked
2. **Attendance Management**: Show all upcoming and past sessions for attendance tracking
3. **Schedule Overview**: Provide a comprehensive view of when classes occur
4. **Mobile Apps**: Generate attendance marking interfaces

## Future Enhancements

- **has_attendance field**: Currently always returns `false`. Future implementation will check if attendance records exist for each session date
- **Filtering**: Add query parameters to filter by date range or attendance status
- **Pagination**: Add pagination for threads with many sessions
- **Session Status**: Add status indicators (upcoming, completed, cancelled)

## Related Endpoints

- `GET /attendance?thread_id={id}&attendance_date={date}` - Get attendance records for a specific session
- `POST /attendance` - Mark attendance for a specific session
- `GET /thread/{id}/schedules` - Get the weekly schedule template for a thread
