package course

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v4"
	"github.com/olzzhas/edunite-server/course_service/internal/database"
	coursepb "github.com/olzzhas/edunite-server/course_service/pb/course"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type Service struct {
	repo database.CourseRepository
	coursepb.UnimplementedCourseServiceServer
}

// NewCourseService – конструктор, инициализирующий Service с репозиторием курсов
func NewCourseService(repo database.CourseRepository) *Service {
	return &Service{repo: repo}
}

// CreateCourse создать курс (gRPC метод)
func (s *Service) CreateCourse(ctx context.Context, req *coursepb.CreateCourseRequest) (*coursepb.CourseResponse, error) {
	course := &database.Course{
		Title:          req.GetTitle(),
		Description:    req.GetDescription(),
		BannerImageUrl: req.GetBannerImageUrl(),
	}

	if err := s.repo.CreateCourse(ctx, course); err != nil {
		return nil, err
	}

	// Add prerequisites if provided
	for _, prerequisiteID := range req.GetPrerequisiteCourseIds() {
		if err := s.repo.AddPrerequisite(ctx, course.ID, prerequisiteID); err != nil {
			return nil, err
		}
	}

	return &coursepb.CourseResponse{
		Id:             course.ID,
		Title:          course.Title,
		Description:    course.Description,
		BannerImageUrl: course.BannerImageUrl,
		CreatedAt:      timestamppb.New(course.CreatedAt),
		UpdatedAt:      timestamppb.New(course.UpdatedAt),
	}, nil
}

// GetCourseByID получить курс по айди (gRPC метод)
func (s *Service) GetCourseByID(ctx context.Context, req *coursepb.GetCourseByIDRequest) (*coursepb.CourseResponse, error) {
	course, err := s.repo.GetCourse(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, status.Errorf(codes.NotFound, "course not found")
		}

		return nil, status.Errorf(codes.Internal,
			"error fetching course: %v", err)
	}
	return convertCourseToPB(course), nil
}

// GetAllCourses получить все курсы (gRPC метод)
func (s *Service) GetAllCourses(ctx context.Context, req *coursepb.EmptyRequest) (*coursepb.CoursesResponse, error) {
	courses, err := s.repo.GetAllCourses(ctx)
	if err != nil {
		return nil, status.Errorf(codes.Internal,
			"could not list courses: %v", err)
	}

	pbCourses := make([]*coursepb.CourseResponse, 0, len(courses))
	for _, c := range courses {
		pbCourses = append(pbCourses, convertCourseToPB(c))
	}
	return &coursepb.CoursesResponse{Courses: pbCourses}, nil
}

// UpdateCourse обновить курс по айди (gRPC метод)
func (s *Service) UpdateCourse(ctx context.Context, req *coursepb.UpdateCourseByIDRequest) (*coursepb.CourseResponse, error) {
	// Проверяем существование курса
	existing, err := s.repo.GetCourse(ctx, req.GetId())
	if err != nil {
		return nil, status.Errorf(codes.NotFound,
			"course not found: %v", err)
	}

	existing.Title = req.GetTitle()
	existing.Description = req.GetDescription()
	existing.BannerImageUrl = req.GetBannerImageUrl()

	v := validator.New()
	database.ValidateCourse(v, existing)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument,
			"validation failed: %v", v.Errors)
	}

	if err := s.repo.UpdateCourse(ctx, existing); err != nil {
		return nil, status.Errorf(codes.Internal,
			"could not update course: %v", err)
	}

	return convertCourseToPB(existing), nil
}

// DeleteCourse удалить курс по айди (gRPC метод)
func (s *Service) DeleteCourse(ctx context.Context, req *coursepb.DeleteCourseByIDRequest) (*coursepb.EmptyResponse, error) {
	if err := s.repo.DeleteCourse(ctx, req.GetId()); err != nil {
		return nil, status.Errorf(codes.NotFound,
			"could not delete course: %v", err)
	}
	return &coursepb.EmptyResponse{}, nil
}

// AddCourseToDegree добавляет курс к программе
func (s *Service) AddCourseToDegree(ctx context.Context, req *coursepb.AddCourseToDegreeRequest) (*coursepb.DegreeCourseResponse, error) {
	v := validator.New()
	v.Check(req.GetDegreeId() > 0, "degree_id", "must be provided and > 0")
	v.Check(req.GetCourseId() > 0, "course_id", "must be provided and > 0")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	degreeCourse := &database.DegreeCourse{
		DegreeID:   req.GetDegreeId(),
		CourseID:   req.GetCourseId(),
		IsRequired: req.GetIsRequired(),
	}

	if req.GetSemesterNumber() > 0 {
		semesterNum := int(req.GetSemesterNumber())
		degreeCourse.SemesterNumber = &semesterNum
	}

	if err := s.repo.AddCourseToDegree(ctx, degreeCourse); err != nil {
		return nil, status.Errorf(codes.Internal, "could not add course to degree: %v", err)
	}

	response := &coursepb.DegreeCourseResponse{
		Id:         degreeCourse.ID,
		DegreeId:   degreeCourse.DegreeID,
		CourseId:   degreeCourse.CourseID,
		IsRequired: degreeCourse.IsRequired,
		CreatedAt:  timestamppb.New(degreeCourse.CreatedAt),
		UpdatedAt:  timestamppb.New(degreeCourse.UpdatedAt),
	}

	if degreeCourse.SemesterNumber != nil {
		response.SemesterNumber = int32(*degreeCourse.SemesterNumber)
	}

	return response, nil
}

// RemoveCourseFromDegree удаляет курс из программы
func (s *Service) RemoveCourseFromDegree(ctx context.Context, req *coursepb.RemoveCourseFromDegreeRequest) (*coursepb.EmptyResponse, error) {
	v := validator.New()
	v.Check(req.GetDegreeId() > 0, "degree_id", "must be provided and > 0")
	v.Check(req.GetCourseId() > 0, "course_id", "must be provided and > 0")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	if err := s.repo.RemoveCourseFromDegree(ctx, req.GetDegreeId(), req.GetCourseId()); err != nil {
		return nil, status.Errorf(codes.Internal, "could not remove course from degree: %v", err)
	}

	return &coursepb.EmptyResponse{}, nil
}

// GetCoursesByDegree возвращает все курсы для определенной программы
func (s *Service) GetCoursesByDegree(ctx context.Context, req *coursepb.GetCoursesByDegreeRequest) (*coursepb.CoursesResponse, error) {
	v := validator.New()
	v.Check(req.GetDegreeId() > 0, "degree_id", "must be provided and > 0")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	courses, err := s.repo.GetCoursesByDegree(ctx, req.GetDegreeId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not get courses by degree: %v", err)
	}

	pbCourses := make([]*coursepb.CourseResponse, 0, len(courses))
	for _, c := range courses {
		pbCourses = append(pbCourses, convertCourseToPB(c))
	}

	return &coursepb.CoursesResponse{Courses: pbCourses}, nil
}

// GetCoursesForStudent возвращает курсы, доступные студенту на основе его программы
func (s *Service) GetCoursesForStudent(ctx context.Context, req *coursepb.GetCoursesForStudentRequest) (*coursepb.CoursesResponse, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be provided and > 0")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	courses, err := s.repo.GetCoursesForStudentDegree(ctx, req.GetUserId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not get courses for student: %v", err)
	}

	pbCourses := make([]*coursepb.CourseResponse, 0, len(courses))
	for _, c := range courses {
		pbCourses = append(pbCourses, convertCourseToPB(c))
	}

	return &coursepb.CoursesResponse{Courses: pbCourses}, nil
}

// convertCourseToPB – вспомогательная функция для преобразования структуры репозитория в protobuf-ответ
func convertCourseToPB(c *database.Course) *coursepb.CourseResponse {
	return &coursepb.CourseResponse{
		Id:             c.ID,
		Title:          c.Title,
		Description:    c.Description,
		BannerImageUrl: c.BannerImageUrl,
		CreatedAt:      timestamppb.New(c.CreatedAt),
		UpdatedAt:      timestamppb.New(c.UpdatedAt),
	}
}
