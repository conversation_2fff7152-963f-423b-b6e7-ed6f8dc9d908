package utils

// ConvertNumericToLetterGrade converts a numeric grade (0-100) to a letter grade
func ConvertNumericToLetterGrade(numericGrade float64) string {
	switch {
	case numericGrade >= 95:
		return "A+"
	case numericGrade >= 90:
		return "A"
	case numericGrade >= 85:
		return "A-"
	case numericGrade >= 80:
		return "B+"
	case numericGrade >= 75:
		return "B"
	case numericGrade >= 70:
		return "B-"
	case numericGrade >= 65:
		return "C+"
	case numericGrade >= 60:
		return "C"
	case numericGrade >= 55:
		return "C-"
	case numericGrade >= 50:
		return "D"
	default:
		return "F"
	}
}

// ConvertNumericToGradePoints converts a numeric grade (0-100) to grade points (0.0-4.0)
func ConvertNumericToGradePoints(numericGrade float64) float64 {
	switch {
	case numericGrade >= 95:
		return 4.0
	case numericGrade >= 90:
		return 3.7
	case numericGrade >= 85:
		return 3.3
	case numericGrade >= 80:
		return 3.0
	case numericGrade >= 75:
		return 2.7
	case numericGrade >= 70:
		return 2.3
	case numericGrade >= 65:
		return 2.0
	case numericGrade >= 60:
		return 1.7
	case numericGrade >= 55:
		return 1.3
	case numericGrade >= 50:
		return 1.0
	default:
		return 0.0
	}
}
