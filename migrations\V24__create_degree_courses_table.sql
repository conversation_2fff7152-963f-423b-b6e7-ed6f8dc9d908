-- Create degree_courses table to link courses with degree programs
CREATE TABLE IF NOT EXISTS degree_courses (
    id BIGSERIAL PRIMARY KEY,
    degree_id BIGINT NOT NULL REFERENCES degrees(id) ON DELETE CASCADE,
    course_id BIGINT NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    is_required BOOLEAN DEFAULT TRUE, -- Whether this course is required or elective
    semester_number INT, -- Recommended semester for this course (optional)
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(degree_id, course_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_degree_courses_degree_id ON degree_courses(degree_id);
CREATE INDEX IF NOT EXISTS idx_degree_courses_course_id ON degree_courses(course_id);

-- Create trigger for updated_at
CREATE TRIGGER trg_degree_courses_updated
    BEFORE UPDATE ON degree_courses
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

-- Insert some sample data to link existing courses with degrees
-- Assuming we have some courses and degrees already created
-- This is just example data - adjust based on your actual data

-- Link Computer Science courses to CS Bachelor degree (assuming degree_id = 1)
INSERT INTO degree_courses (degree_id, course_id, is_required, semester_number) 
SELECT 1, id, TRUE, 1 FROM courses WHERE title ILIKE '%programming%' OR title ILIKE '%computer%' LIMIT 3
ON CONFLICT (degree_id, course_id) DO NOTHING;

-- Link Math courses to Math Bachelor degree (assuming degree_id = 2)
INSERT INTO degree_courses (degree_id, course_id, is_required, semester_number) 
SELECT 2, id, TRUE, 1 FROM courses WHERE title ILIKE '%math%' OR title ILIKE '%calculus%' LIMIT 3
ON CONFLICT (degree_id, course_id) DO NOTHING;
