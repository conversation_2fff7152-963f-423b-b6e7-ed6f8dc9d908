# Тестирование системы управления программами и курсами

## Шаг 1: Создание админа и получение токена

```bash
# Создать админа
curl -X POST http://localhost:8081/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Admin",
    "surname": "User",
    "email": "<EMAIL>",
    "username": "admin",
    "password": "admin123",
    "role": "admin"
  }'

# Получить токен
curl -X POST http://localhost:8081/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

## Шаг 2: Создание студента

```bash
# Создать студента
curl -X POST http://localhost:8081/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Student",
    "surname": "Test",
    "email": "<EMAIL>",
    "username": "student",
    "password": "student123",
    "role": "student"
  }'

# Получить токен студента
curl -X POST http://localhost:8081/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "student",
    "password": "student123"
  }'
```

## Шаг 3: Привязка курсов к программе Computer Science

```bash
# Привязать курс "Golang" к программе Computer Science
curl -X POST http://localhost:8081/degree-courses \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "degree_id": 1,
    "course_id": 1,
    "is_required": true,
    "semester_number": 1
  }'

# Привязать курс "Golang Advanced" к программе Computer Science
curl -X POST http://localhost:8081/degree-courses \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "degree_id": 1,
    "course_id": 2,
    "is_required": true,
    "semester_number": 2
  }'
```

## Шаг 4: Назначение студента на программу

```bash
# Назначить студента на программу Computer Science
curl -X POST http://localhost:8081/student-degrees \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": <student_user_id>,
    "degree_id": 1,
    "start_date": "2024-09-01"
  }'
```

## Шаг 5: Тестирование доступа студента

```bash
# Получить курсы, доступные студенту
curl -X GET http://localhost:8081/students/<student_user_id>/available-courses \
  -H "Authorization: Bearer <student_token>"

# Проверить курсы программы Computer Science
curl -X GET http://localhost:8081/degrees/1/courses \
  -H "Authorization: Bearer <student_token>"
```

## Шаг 6: Создание потока и тестирование регистрации

```bash
# Создать семестр (если нужно)
curl -X POST http://localhost:8081/semester \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Fall 2024",
    "start_date": "2024-09-01",
    "end_date": "2024-12-20"
  }'

# Создать поток для курса Golang
curl -X POST http://localhost:8081/thread \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "course_id": 1,
    "semester_id": <semester_id>,
    "teacher_id": <teacher_id>,
    "max_students": 30,
    "title": "Golang - Group A"
  }'

# Студент пытается зарегистрироваться на поток
curl -X POST http://localhost:8081/thread_registrations/register \
  -H "Authorization: Bearer <student_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": <student_user_id>,
    "thread_id": <thread_id>
  }'
```

## Ожидаемые результаты

### Положительные тесты:
1. **Студент видит только курсы своей программы** - запрос `/students/{id}/available-courses` возвращает только курсы Computer Science
2. **Студент может регистрироваться на курсы своей программы** - регистрация на поток Golang проходит успешно
3. **Админ может управлять связями** - добавление/удаление курсов из программы работает

### Негативные тесты:
1. **Студент не может регистрироваться на курсы других программ** - если создать курс не привязанный к программе студента, регистрация должна быть отклонена
2. **Неавторизованные пользователи не могут управлять связями** - запросы без админских прав отклоняются

## Проверка в базе данных

```sql
-- Проверить связи курсов и программ
SELECT 
    d.name as degree_name,
    c.title as course_title,
    dc.is_required,
    dc.semester_number
FROM degree_courses dc
JOIN degrees d ON dc.degree_id = d.id
JOIN courses c ON dc.course_id = c.id;

-- Проверить студентов и их программы
SELECT 
    u.name,
    u.surname,
    d.name as degree_name,
    sd.status
FROM student_degrees sd
JOIN users u ON sd.user_id = u.id
JOIN degrees d ON sd.degree_id = d.id;
```
