# API для управления программами обучения - Degree Courses

## Новые эндпоинты

### 1. Добавить курс к программе
**POST** `/degree-courses`

**Headers:**
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "degree_id": 1,
  "course_id": 2,
  "is_required": true,
  "semester_number": 1
}
```

**Response (201):**
```json
{
  "id": 1,
  "degree_id": 1,
  "course_id": 2,
  "is_required": true,
  "semester_number": 1,
  "created_at": {
    "seconds": 1749739135,
    "nanos": 975903000
  },
  "updated_at": {
    "seconds": 1749739135,
    "nanos": 975903000
  }
}
```

**cURL:**
```bash
curl -X POST http://localhost:8081/degree-courses \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "degree_id": 1,
    "course_id": 2,
    "is_required": true,
    "semester_number": 1
  }'
```

---

### 2. Получить курсы программы
**GET** `/degree-courses/degree/{degree_id}/courses`

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Response (200):**
```json
{
  "courses": [
    {
      "id": 1,
      "title": "Основы программирования",
      "description": "Введение в программирование на Python",
      "banner_image_url": "course-images/python-basics.jpg",
      "created_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      },
      "updated_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      }
    },
    {
      "id": 2,
      "title": "Веб-разработка",
      "description": "HTML, CSS, JavaScript",
      "banner_image_url": "course-images/web-dev.jpg",
      "created_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      },
      "updated_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      }
    }
  ]
}
```

**cURL:**
```bash
curl -X GET http://localhost:8081/degree-courses/degree/1/courses \
  -H "Authorization: Bearer <admin_token>"
```

---

### 3. Удалить курс из программы
**DELETE** `/degree-courses/degree/{degree_id}/course/{course_id}`

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Response (200):**
```json
{
  "message": "Course removed from degree successfully"
}
```

**cURL:**
```bash
curl -X DELETE http://localhost:8081/degree-courses/degree/1/course/2 \
  -H "Authorization: Bearer <admin_token>"
```

---

### 4. Получить доступные курсы для студента
**GET** `/students/{user_id}/available-courses`

**Headers:**
```
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "courses": [
    {
      "id": 1,
      "title": "Доступный курс",
      "description": "Курс доступен студенту",
      "banner_image_url": "course-images/available.jpg",
      "created_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      },
      "updated_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      }
    }
  ]
}
```

**cURL:**
```bash
curl -X GET http://localhost:8081/students/123/available-courses \
  -H "Authorization: Bearer <token>"
```

## Параметры

### Для добавления курса к программе:
- `degree_id` (int, обязательно) - ID программы обучения
- `course_id` (int, обязательно) - ID курса
- `is_required` (bool, опционально) - Обязательный курс (по умолчанию false)
- `semester_number` (int, опционально) - Номер семестра

## Коды ошибок
- `400` - Неверные данные запроса
- `401` - Требуется авторизация
- `403` - Недостаточно прав (только для админов)
- `404` - Ресурс не найден
- `500` - Внутренняя ошибка сервера

## Доступ
- Эндпоинты `/degree-courses/*` - только администраторы
- Эндпоинт `/students/{user_id}/available-courses` - аутентифицированные пользователи
