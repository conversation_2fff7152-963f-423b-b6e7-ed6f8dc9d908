package clients

import (
	"context"

	coursepb "github.com/olzzhas/edunite-server/course_service/pb/course"

	"google.golang.org/grpc"
)

type CourseClient struct {
	client coursepb.CourseServiceClient
}

// NewCourseClient создает новый экземпляр CourseClient с подключением к gRPC
func NewCourseClient(conn *grpc.ClientConn) *CourseClient {
	return &CourseClient{
		client: coursepb.NewCourseServiceClient(conn),
	}
}

// GetClient returns the underlying gRPC client
func (c *CourseClient) GetClient() coursepb.CourseServiceClient {
	return c.client
}

// CreateCourse вызывает gRPC метод CreateCourse
func (c *CourseClient) CreateCourse(ctx context.Context, title, description string, prerequisiteCourseIDs []int64) (*coursepb.CourseResponse, error) {
	req := &coursepb.CreateCourseRequest{
		Title:                 title,
		Description:           description,
		PrerequisiteCourseIds: prerequisiteCourseIDs,
	}
	return c.client.CreateCourse(ctx, req)
}

// GetCourseByID вызывает gRPC метод GetCourseByID
func (c *CourseClient) GetCourseByID(ctx context.Context, courseID int64) (*coursepb.CourseResponse, error) {
	req := &coursepb.GetCourseByIDRequest{Id: courseID}
	return c.client.GetCourseByID(ctx, req)
}

// GetAllCourses вызывает gRPC метод GetAllCourses
func (c *CourseClient) GetAllCourses(ctx context.Context) (*coursepb.CoursesResponse, error) {
	req := &coursepb.EmptyRequest{}
	return c.client.GetAllCourses(ctx, req)
}

// UpdateCourseByID вызывает gRPC метод UpdateCourseByID
func (c *CourseClient) UpdateCourseByID(ctx context.Context, courseID int64, title, description, bannerURL string) (*coursepb.CourseResponse, error) {
	req := &coursepb.UpdateCourseByIDRequest{
		Id:             courseID,
		Title:          title,
		Description:    description,
		BannerImageUrl: bannerURL,
	}
	return c.client.UpdateCourse(ctx, req)
}

// DeleteCourseByID вызывает gRPC метод DeleteCourseByID
func (c *CourseClient) DeleteCourseByID(ctx context.Context, courseID int64) error {
	req := &coursepb.DeleteCourseByIDRequest{Id: courseID}
	_, err := c.client.DeleteCourse(ctx, req)
	return err
}
