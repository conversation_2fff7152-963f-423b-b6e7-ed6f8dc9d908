package database

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

var (
	ErrTranscriptNotFound       = errors.New("transcript not found")
	ErrTranscriptEntryNotFound  = errors.New("transcript entry not found")
	ErrDegreeNotFound           = errors.New("degree not found")
	ErrStudentDegreeNotFound    = errors.New("student degree not found")
	ErrDuplicateTranscript      = errors.New("transcript already exists for this user and degree")
	ErrDuplicateTranscriptEntry = errors.New("transcript entry already exists for this course and semester")
)

// Degree model
type Degree struct {
	ID              int64     `json:"id"`
	Name            string    `json:"name"`
	Level           string    `json:"level"`
	Description     string    `json:"description"`
	RequiredCredits int32     `json:"required_credits"`
	MinGPA          float64   `json:"min_gpa"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// AcademicTranscript model
type AcademicTranscript struct {
	ID                    int64      `json:"id"`
	UserID                int64      `json:"user_id"`
	DegreeID              *int64     `json:"degree_id"`
	CumulativeGPA         float64    `json:"cumulative_gpa"`
	TotalCreditsAttempted int32      `json:"total_credits_attempted"`
	TotalCreditsEarned    int32      `json:"total_credits_earned"`
	AcademicStanding      string     `json:"academic_standing"`
	GraduationDate        *time.Time `json:"graduation_date"`
	CreatedAt             time.Time  `json:"created_at"`
	UpdatedAt             time.Time  `json:"updated_at"`
	Degree                *Degree    `json:"degree,omitempty"`
}

// TranscriptEntry model
type TranscriptEntry struct {
	ID             int64      `json:"id"`
	TranscriptID   int64      `json:"transcript_id"`
	CourseID       int64      `json:"course_id"`
	ThreadID       *int64     `json:"thread_id"`
	SemesterID     int64      `json:"semester_id"`
	GradeLetter    *string    `json:"grade_letter"`
	GradeNumeric   *float64   `json:"grade_numeric"`
	GradePoints    *float64   `json:"grade_points"`
	Credits        int32      `json:"credits"`
	IsTransfer     bool       `json:"is_transfer"`
	IsRepeated     bool       `json:"is_repeated"`
	CompletionDate *time.Time `json:"completion_date"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
	CourseTitle    string     `json:"course_title,omitempty"`
	SemesterName   string     `json:"semester_name,omitempty"`
}

// StudentDegree model
type StudentDegree struct {
	ID                     int64      `json:"id"`
	UserID                 int64      `json:"user_id"`
	DegreeID               int64      `json:"degree_id"`
	Status                 string     `json:"status"`
	StartDate              time.Time  `json:"start_date"`
	ExpectedGraduationDate *time.Time `json:"expected_graduation_date"`
	ActualGraduationDate   *time.Time `json:"actual_graduation_date"`
	FinalGPA               *float64   `json:"final_gpa"`
	CreatedAt              time.Time  `json:"created_at"`
	UpdatedAt              time.Time  `json:"updated_at"`
	Degree                 *Degree    `json:"degree,omitempty"`
}

// TranscriptRepository interface
type TranscriptRepository interface {
	// Degree methods
	CreateDegree(ctx context.Context, degree *Degree) error
	GetDegree(ctx context.Context, id int64) (*Degree, error)
	ListDegrees(ctx context.Context, level string, offset, limit int) ([]*Degree, int, error)
	UpdateDegree(ctx context.Context, degree *Degree) error
	DeleteDegree(ctx context.Context, id int64) error

	// Academic transcript methods
	CreateTranscript(ctx context.Context, transcript *AcademicTranscript) error
	GetTranscript(ctx context.Context, userID int64, degreeID *int64) (*AcademicTranscript, error)
	GetTranscriptByID(ctx context.Context, id int64) (*AcademicTranscript, error)
	UpdateTranscript(ctx context.Context, transcript *AcademicTranscript) error
	DeleteTranscript(ctx context.Context, id int64) error

	// Transcript entry methods
	AddTranscriptEntry(ctx context.Context, entry *TranscriptEntry) error
	GetTranscriptEntries(ctx context.Context, transcriptID int64, semesterID *int64) ([]*TranscriptEntry, error)
	GetTranscriptEntry(ctx context.Context, id int64) (*TranscriptEntry, error)
	UpdateTranscriptEntry(ctx context.Context, entry *TranscriptEntry) error
	DeleteTranscriptEntry(ctx context.Context, id int64) error

	// Student degree methods
	CreateStudentDegree(ctx context.Context, studentDegree *StudentDegree) error
	GetStudentDegrees(ctx context.Context, userID int64) ([]*StudentDegree, error)
	ListStudentDegrees(ctx context.Context, userID, degreeID *int64, status string, offset, limit int) ([]*StudentDegree, int, error)
	GetStudentDegree(ctx context.Context, id int64) (*StudentDegree, error)
	UpdateStudentDegree(ctx context.Context, studentDegree *StudentDegree) error
	DeleteStudentDegree(ctx context.Context, id int64) error

	// GPA calculation methods
	CalculateGPA(ctx context.Context, transcriptID int64) (float64, int32, int32, error)
	UpdateGPA(ctx context.Context, transcriptID int64) error
}

// transcriptRepository implementation
type transcriptRepository struct {
	db *pgxpool.Pool
}

// NewTranscriptRepository creates a new transcript repository
func NewTranscriptRepository(db *pgxpool.Pool) TranscriptRepository {
	return &transcriptRepository{db: db}
}

// Degree methods
func (r *transcriptRepository) CreateDegree(ctx context.Context, degree *Degree) error {
	query := `
		INSERT INTO degrees (name, level, description, required_credits, min_gpa)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id, created_at, updated_at`

	err := r.db.QueryRow(ctx, query,
		degree.Name,
		degree.Level,
		degree.Description,
		degree.RequiredCredits,
		degree.MinGPA,
	).Scan(&degree.ID, &degree.CreatedAt, &degree.UpdatedAt)

	return err
}

func (r *transcriptRepository) GetDegree(ctx context.Context, id int64) (*Degree, error) {
	query := `
		SELECT id, name, level, description, required_credits, min_gpa, created_at, updated_at
		FROM degrees
		WHERE id = $1`

	degree := &Degree{}
	err := r.db.QueryRow(ctx, query, id).Scan(
		&degree.ID,
		&degree.Name,
		&degree.Level,
		&degree.Description,
		&degree.RequiredCredits,
		&degree.MinGPA,
		&degree.CreatedAt,
		&degree.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrDegreeNotFound
		}
		return nil, err
	}

	return degree, nil
}

func (r *transcriptRepository) ListDegrees(ctx context.Context, level string, offset, limit int) ([]*Degree, int, error) {
	var query string
	var args []interface{}
	var countQuery string

	if level != "" {
		query = `
			SELECT id, name, level, description, required_credits, min_gpa, created_at, updated_at
			FROM degrees
			WHERE level = $1
			ORDER BY name
			LIMIT $2 OFFSET $3`
		countQuery = `SELECT COUNT(*) FROM degrees WHERE level = $1`
		args = []interface{}{level, limit, offset}
	} else {
		query = `
			SELECT id, name, level, description, required_credits, min_gpa, created_at, updated_at
			FROM degrees
			ORDER BY name
			LIMIT $1 OFFSET $2`
		countQuery = `SELECT COUNT(*) FROM degrees`
		args = []interface{}{limit, offset}
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var degrees []*Degree
	for rows.Next() {
		degree := &Degree{}
		err := rows.Scan(
			&degree.ID,
			&degree.Name,
			&degree.Level,
			&degree.Description,
			&degree.RequiredCredits,
			&degree.MinGPA,
			&degree.CreatedAt,
			&degree.UpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}
		degrees = append(degrees, degree)
	}

	// Get total count
	var totalCount int
	if level != "" {
		err = r.db.QueryRow(ctx, countQuery, level).Scan(&totalCount)
	} else {
		err = r.db.QueryRow(ctx, countQuery).Scan(&totalCount)
	}
	if err != nil {
		return nil, 0, err
	}

	return degrees, totalCount, nil
}

func (r *transcriptRepository) UpdateDegree(ctx context.Context, degree *Degree) error {
	query := `
		UPDATE degrees
		SET name = $2, level = $3, description = $4, required_credits = $5, min_gpa = $6, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`

	err := r.db.QueryRow(ctx, query,
		degree.ID,
		degree.Name,
		degree.Level,
		degree.Description,
		degree.RequiredCredits,
		degree.MinGPA,
	).Scan(&degree.UpdatedAt)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ErrDegreeNotFound
		}
		return err
	}

	return nil
}

func (r *transcriptRepository) DeleteDegree(ctx context.Context, id int64) error {
	query := `DELETE FROM degrees WHERE id = $1`
	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return err
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return ErrDegreeNotFound
	}

	return nil
}

// Academic transcript methods
func (r *transcriptRepository) CreateTranscript(ctx context.Context, transcript *AcademicTranscript) error {
	query := `
		INSERT INTO academic_transcripts (user_id, degree_id, cumulative_gpa, total_credits_attempted, total_credits_earned, academic_standing, graduation_date)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id, created_at, updated_at`

	err := r.db.QueryRow(ctx, query,
		transcript.UserID,
		transcript.DegreeID,
		transcript.CumulativeGPA,
		transcript.TotalCreditsAttempted,
		transcript.TotalCreditsEarned,
		transcript.AcademicStanding,
		transcript.GraduationDate,
	).Scan(&transcript.ID, &transcript.CreatedAt, &transcript.UpdatedAt)

	return err
}

func (r *transcriptRepository) GetTranscript(ctx context.Context, userID int64, degreeID *int64) (*AcademicTranscript, error) {
	var query string
	var args []interface{}

	if degreeID != nil {
		query = `
			SELECT t.id, t.user_id, t.degree_id, t.cumulative_gpa, t.total_credits_attempted,
				   t.total_credits_earned, t.academic_standing, t.graduation_date, t.created_at, t.updated_at,
				   d.id, d.name, d.level, d.description, d.required_credits, d.min_gpa, d.created_at, d.updated_at
			FROM academic_transcripts t
			LEFT JOIN degrees d ON t.degree_id = d.id
			WHERE t.user_id = $1 AND t.degree_id = $2`
		args = []interface{}{userID, *degreeID}
	} else {
		query = `
			SELECT t.id, t.user_id, t.degree_id, t.cumulative_gpa, t.total_credits_attempted,
				   t.total_credits_earned, t.academic_standing, t.graduation_date, t.created_at, t.updated_at,
				   d.id, d.name, d.level, d.description, d.required_credits, d.min_gpa, d.created_at, d.updated_at
			FROM academic_transcripts t
			LEFT JOIN degrees d ON t.degree_id = d.id
			WHERE t.user_id = $1
			ORDER BY t.created_at DESC
			LIMIT 1`
		args = []interface{}{userID}
	}

	transcript := &AcademicTranscript{}

	// Use nullable types for degree fields since LEFT JOIN can return NULLs
	var degreeID_db sql.NullInt64
	var degreeName sql.NullString
	var degreeLevel sql.NullString
	var degreeDescription sql.NullString
	var degreeRequiredCredits sql.NullInt32
	var degreeMinGPA sql.NullFloat64
	var degreeCreatedAt sql.NullTime
	var degreeUpdatedAt sql.NullTime

	err := r.db.QueryRow(ctx, query, args...).Scan(
		&transcript.ID,
		&transcript.UserID,
		&transcript.DegreeID,
		&transcript.CumulativeGPA,
		&transcript.TotalCreditsAttempted,
		&transcript.TotalCreditsEarned,
		&transcript.AcademicStanding,
		&transcript.GraduationDate,
		&transcript.CreatedAt,
		&transcript.UpdatedAt,
		&degreeID_db,
		&degreeName,
		&degreeLevel,
		&degreeDescription,
		&degreeRequiredCredits,
		&degreeMinGPA,
		&degreeCreatedAt,
		&degreeUpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrTranscriptNotFound
		}
		return nil, err
	}

	// If degree exists, populate it
	if transcript.DegreeID != nil && degreeID_db.Valid {
		degree := &Degree{
			ID:              degreeID_db.Int64,
			Name:            degreeName.String,
			Level:           degreeLevel.String,
			Description:     degreeDescription.String,
			RequiredCredits: degreeRequiredCredits.Int32,
			MinGPA:          degreeMinGPA.Float64,
			CreatedAt:       degreeCreatedAt.Time,
			UpdatedAt:       degreeUpdatedAt.Time,
		}
		transcript.Degree = degree
	}

	return transcript, nil
}

func (r *transcriptRepository) GetTranscriptByID(ctx context.Context, id int64) (*AcademicTranscript, error) {
	query := `
		SELECT t.id, t.user_id, t.degree_id, t.cumulative_gpa, t.total_credits_attempted,
			   t.total_credits_earned, t.academic_standing, t.graduation_date, t.created_at, t.updated_at,
			   d.id, d.name, d.level, d.description, d.required_credits, d.min_gpa, d.created_at, d.updated_at
		FROM academic_transcripts t
		LEFT JOIN degrees d ON t.degree_id = d.id
		WHERE t.id = $1`

	transcript := &AcademicTranscript{}

	// Use nullable types for degree fields since LEFT JOIN can return NULLs
	var degreeID_db sql.NullInt64
	var degreeName sql.NullString
	var degreeLevel sql.NullString
	var degreeDescription sql.NullString
	var degreeRequiredCredits sql.NullInt32
	var degreeMinGPA sql.NullFloat64
	var degreeCreatedAt sql.NullTime
	var degreeUpdatedAt sql.NullTime

	err := r.db.QueryRow(ctx, query, id).Scan(
		&transcript.ID,
		&transcript.UserID,
		&transcript.DegreeID,
		&transcript.CumulativeGPA,
		&transcript.TotalCreditsAttempted,
		&transcript.TotalCreditsEarned,
		&transcript.AcademicStanding,
		&transcript.GraduationDate,
		&transcript.CreatedAt,
		&transcript.UpdatedAt,
		&degreeID_db,
		&degreeName,
		&degreeLevel,
		&degreeDescription,
		&degreeRequiredCredits,
		&degreeMinGPA,
		&degreeCreatedAt,
		&degreeUpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrTranscriptNotFound
		}
		return nil, err
	}

	// If degree exists, populate it
	if transcript.DegreeID != nil && degreeID_db.Valid {
		degree := &Degree{
			ID:              degreeID_db.Int64,
			Name:            degreeName.String,
			Level:           degreeLevel.String,
			Description:     degreeDescription.String,
			RequiredCredits: degreeRequiredCredits.Int32,
			MinGPA:          degreeMinGPA.Float64,
			CreatedAt:       degreeCreatedAt.Time,
			UpdatedAt:       degreeUpdatedAt.Time,
		}
		transcript.Degree = degree
	}

	return transcript, nil
}

func (r *transcriptRepository) UpdateTranscript(ctx context.Context, transcript *AcademicTranscript) error {
	query := `
		UPDATE academic_transcripts
		SET cumulative_gpa = $2, total_credits_attempted = $3, total_credits_earned = $4,
			academic_standing = $5, graduation_date = $6, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`

	err := r.db.QueryRow(ctx, query,
		transcript.ID,
		transcript.CumulativeGPA,
		transcript.TotalCreditsAttempted,
		transcript.TotalCreditsEarned,
		transcript.AcademicStanding,
		transcript.GraduationDate,
	).Scan(&transcript.UpdatedAt)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ErrTranscriptNotFound
		}
		return err
	}

	return nil
}

func (r *transcriptRepository) DeleteTranscript(ctx context.Context, id int64) error {
	query := `DELETE FROM academic_transcripts WHERE id = $1`
	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return err
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return ErrTranscriptNotFound
	}

	return nil
}

// Transcript entry methods
func (r *transcriptRepository) AddTranscriptEntry(ctx context.Context, entry *TranscriptEntry) error {
	query := `
		INSERT INTO transcript_entries (transcript_id, course_id, thread_id, semester_id, grade_letter,
										grade_numeric, grade_points, credits, is_transfer, is_repeated, completion_date)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		RETURNING id, created_at, updated_at`

	err := r.db.QueryRow(ctx, query,
		entry.TranscriptID,
		entry.CourseID,
		entry.ThreadID,
		entry.SemesterID,
		entry.GradeLetter,
		entry.GradeNumeric,
		entry.GradePoints,
		entry.Credits,
		entry.IsTransfer,
		entry.IsRepeated,
		entry.CompletionDate,
	).Scan(&entry.ID, &entry.CreatedAt, &entry.UpdatedAt)

	return err
}

func (r *transcriptRepository) GetTranscriptEntries(ctx context.Context, transcriptID int64, semesterID *int64) ([]*TranscriptEntry, error) {
	var query string
	var args []interface{}

	if semesterID != nil {
		query = `
			SELECT te.id, te.transcript_id, te.course_id, te.thread_id, te.semester_id,
				   te.grade_letter, te.grade_numeric, te.grade_points, te.credits,
				   te.is_transfer, te.is_repeated, te.completion_date, te.created_at, te.updated_at,
				   c.title, s.name
			FROM transcript_entries te
			LEFT JOIN courses c ON te.course_id = c.id
			LEFT JOIN semesters s ON te.semester_id = s.id
			WHERE te.transcript_id = $1 AND te.semester_id = $2
			ORDER BY s.start_date DESC, c.title`
		args = []interface{}{transcriptID, *semesterID}
	} else {
		query = `
			SELECT te.id, te.transcript_id, te.course_id, te.thread_id, te.semester_id,
				   te.grade_letter, te.grade_numeric, te.grade_points, te.credits,
				   te.is_transfer, te.is_repeated, te.completion_date, te.created_at, te.updated_at,
				   c.title, s.name
			FROM transcript_entries te
			LEFT JOIN courses c ON te.course_id = c.id
			LEFT JOIN semesters s ON te.semester_id = s.id
			WHERE te.transcript_id = $1
			ORDER BY s.start_date DESC, c.title`
		args = []interface{}{transcriptID}
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var entries []*TranscriptEntry
	for rows.Next() {
		entry := &TranscriptEntry{}
		err := rows.Scan(
			&entry.ID,
			&entry.TranscriptID,
			&entry.CourseID,
			&entry.ThreadID,
			&entry.SemesterID,
			&entry.GradeLetter,
			&entry.GradeNumeric,
			&entry.GradePoints,
			&entry.Credits,
			&entry.IsTransfer,
			&entry.IsRepeated,
			&entry.CompletionDate,
			&entry.CreatedAt,
			&entry.UpdatedAt,
			&entry.CourseTitle,
			&entry.SemesterName,
		)
		if err != nil {
			return nil, err
		}
		entries = append(entries, entry)
	}

	return entries, nil
}

func (r *transcriptRepository) GetTranscriptEntry(ctx context.Context, id int64) (*TranscriptEntry, error) {
	query := `
		SELECT te.id, te.transcript_id, te.course_id, te.thread_id, te.semester_id,
			   te.grade_letter, te.grade_numeric, te.grade_points, te.credits,
			   te.is_transfer, te.is_repeated, te.completion_date, te.created_at, te.updated_at,
			   c.title, s.name
		FROM transcript_entries te
		LEFT JOIN courses c ON te.course_id = c.id
		LEFT JOIN semesters s ON te.semester_id = s.id
		WHERE te.id = $1`

	entry := &TranscriptEntry{}
	err := r.db.QueryRow(ctx, query, id).Scan(
		&entry.ID,
		&entry.TranscriptID,
		&entry.CourseID,
		&entry.ThreadID,
		&entry.SemesterID,
		&entry.GradeLetter,
		&entry.GradeNumeric,
		&entry.GradePoints,
		&entry.Credits,
		&entry.IsTransfer,
		&entry.IsRepeated,
		&entry.CompletionDate,
		&entry.CreatedAt,
		&entry.UpdatedAt,
		&entry.CourseTitle,
		&entry.SemesterName,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrTranscriptEntryNotFound
		}
		return nil, err
	}

	return entry, nil
}

func (r *transcriptRepository) UpdateTranscriptEntry(ctx context.Context, entry *TranscriptEntry) error {
	query := `
		UPDATE transcript_entries
		SET grade_letter = $2, grade_numeric = $3, grade_points = $4, credits = $5,
			is_transfer = $6, is_repeated = $7, completion_date = $8, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`

	err := r.db.QueryRow(ctx, query,
		entry.ID,
		entry.GradeLetter,
		entry.GradeNumeric,
		entry.GradePoints,
		entry.Credits,
		entry.IsTransfer,
		entry.IsRepeated,
		entry.CompletionDate,
	).Scan(&entry.UpdatedAt)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ErrTranscriptEntryNotFound
		}
		return err
	}

	return nil
}

func (r *transcriptRepository) DeleteTranscriptEntry(ctx context.Context, id int64) error {
	query := `DELETE FROM transcript_entries WHERE id = $1`
	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return err
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return ErrTranscriptEntryNotFound
	}

	return nil
}

// Student degree methods
func (r *transcriptRepository) CreateStudentDegree(ctx context.Context, studentDegree *StudentDegree) error {
	query := `
		INSERT INTO student_degrees (user_id, degree_id, status, start_date, expected_graduation_date, actual_graduation_date, final_gpa)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id, created_at, updated_at`

	err := r.db.QueryRow(ctx, query,
		studentDegree.UserID,
		studentDegree.DegreeID,
		studentDegree.Status,
		studentDegree.StartDate,
		studentDegree.ExpectedGraduationDate,
		studentDegree.ActualGraduationDate,
		studentDegree.FinalGPA,
	).Scan(&studentDegree.ID, &studentDegree.CreatedAt, &studentDegree.UpdatedAt)

	return err
}

func (r *transcriptRepository) GetStudentDegrees(ctx context.Context, userID int64) ([]*StudentDegree, error) {
	query := `
		SELECT sd.id, sd.user_id, sd.degree_id, sd.status, sd.start_date,
			   sd.expected_graduation_date, sd.actual_graduation_date, sd.final_gpa,
			   sd.created_at, sd.updated_at,
			   d.id, d.name, d.level, d.description, d.required_credits, d.min_gpa, d.created_at, d.updated_at
		FROM student_degrees sd
		LEFT JOIN degrees d ON sd.degree_id = d.id
		WHERE sd.user_id = $1
		ORDER BY sd.start_date DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var studentDegrees []*StudentDegree
	for rows.Next() {
		studentDegree := &StudentDegree{}

		// Use nullable types for degree fields since LEFT JOIN can return NULLs
		var degreeID_db sql.NullInt64
		var degreeName sql.NullString
		var degreeLevel sql.NullString
		var degreeDescription sql.NullString
		var degreeRequiredCredits sql.NullInt32
		var degreeMinGPA sql.NullFloat64
		var degreeCreatedAt sql.NullTime
		var degreeUpdatedAt sql.NullTime

		err := rows.Scan(
			&studentDegree.ID,
			&studentDegree.UserID,
			&studentDegree.DegreeID,
			&studentDegree.Status,
			&studentDegree.StartDate,
			&studentDegree.ExpectedGraduationDate,
			&studentDegree.ActualGraduationDate,
			&studentDegree.FinalGPA,
			&studentDegree.CreatedAt,
			&studentDegree.UpdatedAt,
			&degreeID_db,
			&degreeName,
			&degreeLevel,
			&degreeDescription,
			&degreeRequiredCredits,
			&degreeMinGPA,
			&degreeCreatedAt,
			&degreeUpdatedAt,
		)
		if err != nil {
			return nil, err
		}

		// If degree exists, populate it
		if degreeID_db.Valid {
			degree := &Degree{
				ID:              degreeID_db.Int64,
				Name:            degreeName.String,
				Level:           degreeLevel.String,
				Description:     degreeDescription.String,
				RequiredCredits: degreeRequiredCredits.Int32,
				MinGPA:          degreeMinGPA.Float64,
				CreatedAt:       degreeCreatedAt.Time,
				UpdatedAt:       degreeUpdatedAt.Time,
			}
			studentDegree.Degree = degree
		}

		studentDegrees = append(studentDegrees, studentDegree)
	}

	return studentDegrees, nil
}

func (r *transcriptRepository) ListStudentDegrees(ctx context.Context, userID, degreeID *int64, status string, offset, limit int) ([]*StudentDegree, int, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	// Build WHERE conditions
	if userID != nil {
		conditions = append(conditions, fmt.Sprintf("sd.user_id = $%d", argIndex))
		args = append(args, *userID)
		argIndex++
	}

	if degreeID != nil {
		conditions = append(conditions, fmt.Sprintf("sd.degree_id = $%d", argIndex))
		args = append(args, *degreeID)
		argIndex++
	}

	if status != "" {
		conditions = append(conditions, fmt.Sprintf("sd.status = $%d", argIndex))
		args = append(args, status)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Main query
	query := fmt.Sprintf(`
		SELECT sd.id, sd.user_id, sd.degree_id, sd.status, sd.start_date,
			   sd.expected_graduation_date, sd.actual_graduation_date, sd.final_gpa,
			   sd.created_at, sd.updated_at,
			   d.id, d.name, d.level, d.description, d.required_credits, d.min_gpa, d.created_at, d.updated_at
		FROM student_degrees sd
		LEFT JOIN degrees d ON sd.degree_id = d.id
		%s
		ORDER BY sd.created_at DESC
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, limit, offset)

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var studentDegrees []*StudentDegree
	for rows.Next() {
		studentDegree := &StudentDegree{}

		// Use nullable types for degree fields since LEFT JOIN can return NULLs
		var degreeID_db sql.NullInt64
		var degreeName sql.NullString
		var degreeLevel sql.NullString
		var degreeDescription sql.NullString
		var degreeRequiredCredits sql.NullInt32
		var degreeMinGPA sql.NullFloat64
		var degreeCreatedAt sql.NullTime
		var degreeUpdatedAt sql.NullTime

		err := rows.Scan(
			&studentDegree.ID,
			&studentDegree.UserID,
			&studentDegree.DegreeID,
			&studentDegree.Status,
			&studentDegree.StartDate,
			&studentDegree.ExpectedGraduationDate,
			&studentDegree.ActualGraduationDate,
			&studentDegree.FinalGPA,
			&studentDegree.CreatedAt,
			&studentDegree.UpdatedAt,
			&degreeID_db,
			&degreeName,
			&degreeLevel,
			&degreeDescription,
			&degreeRequiredCredits,
			&degreeMinGPA,
			&degreeCreatedAt,
			&degreeUpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}

		// If degree exists, populate it
		if degreeID_db.Valid {
			degree := &Degree{
				ID:              degreeID_db.Int64,
				Name:            degreeName.String,
				Level:           degreeLevel.String,
				Description:     degreeDescription.String,
				RequiredCredits: degreeRequiredCredits.Int32,
				MinGPA:          degreeMinGPA.Float64,
				CreatedAt:       degreeCreatedAt.Time,
				UpdatedAt:       degreeUpdatedAt.Time,
			}
			studentDegree.Degree = degree
		}

		studentDegrees = append(studentDegrees, studentDegree)
	}

	// Count query
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM student_degrees sd %s", whereClause)
	var countArgs []interface{}
	if userID != nil {
		countArgs = append(countArgs, *userID)
	}
	if degreeID != nil {
		countArgs = append(countArgs, *degreeID)
	}
	if status != "" {
		countArgs = append(countArgs, status)
	}

	var totalCount int
	err = r.db.QueryRow(ctx, countQuery, countArgs...).Scan(&totalCount)
	if err != nil {
		return nil, 0, err
	}

	return studentDegrees, totalCount, nil
}

func (r *transcriptRepository) GetStudentDegree(ctx context.Context, id int64) (*StudentDegree, error) {
	query := `
		SELECT sd.id, sd.user_id, sd.degree_id, sd.status, sd.start_date,
			   sd.expected_graduation_date, sd.actual_graduation_date, sd.final_gpa,
			   sd.created_at, sd.updated_at,
			   d.id, d.name, d.level, d.description, d.required_credits, d.min_gpa, d.created_at, d.updated_at
		FROM student_degrees sd
		LEFT JOIN degrees d ON sd.degree_id = d.id
		WHERE sd.id = $1`

	studentDegree := &StudentDegree{}

	// Use nullable types for degree fields since LEFT JOIN can return NULLs
	var degreeID_db sql.NullInt64
	var degreeName sql.NullString
	var degreeLevel sql.NullString
	var degreeDescription sql.NullString
	var degreeRequiredCredits sql.NullInt32
	var degreeMinGPA sql.NullFloat64
	var degreeCreatedAt sql.NullTime
	var degreeUpdatedAt sql.NullTime

	err := r.db.QueryRow(ctx, query, id).Scan(
		&studentDegree.ID,
		&studentDegree.UserID,
		&studentDegree.DegreeID,
		&studentDegree.Status,
		&studentDegree.StartDate,
		&studentDegree.ExpectedGraduationDate,
		&studentDegree.ActualGraduationDate,
		&studentDegree.FinalGPA,
		&studentDegree.CreatedAt,
		&studentDegree.UpdatedAt,
		&degreeID_db,
		&degreeName,
		&degreeLevel,
		&degreeDescription,
		&degreeRequiredCredits,
		&degreeMinGPA,
		&degreeCreatedAt,
		&degreeUpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrStudentDegreeNotFound
		}
		return nil, err
	}

	// If degree exists, populate it
	if degreeID_db.Valid {
		degree := &Degree{
			ID:              degreeID_db.Int64,
			Name:            degreeName.String,
			Level:           degreeLevel.String,
			Description:     degreeDescription.String,
			RequiredCredits: degreeRequiredCredits.Int32,
			MinGPA:          degreeMinGPA.Float64,
			CreatedAt:       degreeCreatedAt.Time,
			UpdatedAt:       degreeUpdatedAt.Time,
		}
		studentDegree.Degree = degree
	}

	return studentDegree, nil
}

func (r *transcriptRepository) UpdateStudentDegree(ctx context.Context, studentDegree *StudentDegree) error {
	query := `
		UPDATE student_degrees
		SET status = $2, expected_graduation_date = $3, actual_graduation_date = $4,
			final_gpa = $5, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
		RETURNING updated_at`

	err := r.db.QueryRow(ctx, query,
		studentDegree.ID,
		studentDegree.Status,
		studentDegree.ExpectedGraduationDate,
		studentDegree.ActualGraduationDate,
		studentDegree.FinalGPA,
	).Scan(&studentDegree.UpdatedAt)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ErrStudentDegreeNotFound
		}
		return err
	}

	return nil
}

func (r *transcriptRepository) DeleteStudentDegree(ctx context.Context, id int64) error {
	query := `DELETE FROM student_degrees WHERE id = $1`
	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return err
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return ErrStudentDegreeNotFound
	}

	return nil
}

// GPA calculation methods
func (r *transcriptRepository) CalculateGPA(ctx context.Context, transcriptID int64) (float64, int32, int32, error) {
	query := `
		SELECT
			COALESCE(SUM(CASE WHEN grade_points IS NOT NULL THEN grade_points * credits END), 0)::FLOAT as total_grade_points,
			COALESCE(SUM(CASE WHEN grade_points IS NOT NULL THEN credits END), 0)::FLOAT as total_credits_for_gpa,
			COALESCE(SUM(credits), 0)::FLOAT as total_credits_attempted,
			COALESCE(SUM(CASE WHEN grade_points >= 2.0 THEN credits ELSE 0 END), 0)::FLOAT as total_credits_earned
		FROM transcript_entries
		WHERE transcript_id = $1 AND is_repeated = false`

	var totalGradePoints, totalCreditsForGPA, totalCreditsAttempted, totalCreditsEarned float64

	err := r.db.QueryRow(ctx, query, transcriptID).Scan(
		&totalGradePoints,
		&totalCreditsForGPA,
		&totalCreditsAttempted,
		&totalCreditsEarned,
	)

	if err != nil {
		return 0, 0, 0, err
	}

	var gpa float64
	if totalCreditsForGPA > 0 {
		gpa = totalGradePoints / totalCreditsForGPA
	}

	return gpa, int32(totalCreditsAttempted), int32(totalCreditsEarned), nil
}

func (r *transcriptRepository) UpdateGPA(ctx context.Context, transcriptID int64) error {
	gpa, creditsAttempted, creditsEarned, err := r.CalculateGPA(ctx, transcriptID)
	if err != nil {
		return err
	}

	// Determine academic standing based on GPA
	var academicStanding string
	switch {
	case gpa >= 3.5:
		academicStanding = "good_standing"
	case gpa >= 3.0:
		academicStanding = "good_standing"
	case gpa >= 2.5:
		academicStanding = "academic_warning"
	case gpa >= 2.0:
		academicStanding = "academic_probation"
	default:
		academicStanding = "academic_suspension"
	}

	query := `
		UPDATE academic_transcripts
		SET cumulative_gpa = $2, total_credits_attempted = $3, total_credits_earned = $4,
			academic_standing = $5, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1`

	_, err = r.db.Exec(ctx, query, transcriptID, gpa, creditsAttempted, creditsEarned, academicStanding)
	return err
}
