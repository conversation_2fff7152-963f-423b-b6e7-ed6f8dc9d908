syntax = "proto3";

package coursepb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/olzzhas/edunite-server/course_service/pb/course;coursepb";

service CourseService {
  // course entity
  rpc CreateCourse(CreateCourseRequest) returns (CourseResponse){}
  rpc GetCourseByID(GetCourseByIDRequest) returns (CourseResponse) {}
  rpc GetAllCourses(EmptyRequest) returns(CoursesResponse){}
  rpc UpdateCourse(UpdateCourseByIDRequest) returns (CourseResponse){}
  rpc DeleteCourse(DeleteCourseByIDRequest) returns (EmptyResponse){}

  // degree-course relationship methods
  rpc AddCourseToDegree(AddCourseToDegreeRequest) returns (DegreeCourseResponse){}
  rpc RemoveCourseFromDegree(RemoveCourseFromDegreeRequest) returns (EmptyResponse){}
  rpc GetCoursesByDegree(GetCoursesByDegreeRequest) returns (CoursesResponse){}
  rpc GetCoursesForStudent(GetCoursesForStudentRequest) returns (CoursesResponse){}
}

//-------------------------------//
//        Course Messages        //
//-------------------------------//
message CreateCourseRequest {
  string title = 1;
  string description = 2;
  string banner_image_url = 3;
  repeated int64 prerequisite_course_ids = 4;
}

message GetCourseByIDRequest {
  int64 id = 1;
}

message UpdateCourseByIDRequest {
  int64 id = 1;
  string title = 2;
  string description = 3;
  string banner_image_url = 4;
}

message DeleteCourseByIDRequest {
  int64 id = 1;
}

message CourseResponse {
  int64 id = 1;
  string title = 2;
  string description = 3;
  string banner_image_url = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message CoursesResponse {
  repeated CourseResponse courses = 1;
}

//-------------------------------//
//    Degree-Course Messages     //
//-------------------------------//
message AddCourseToDegreeRequest {
  int64 degree_id = 1;
  int64 course_id = 2;
  bool is_required = 3;
  int32 semester_number = 4;
}

message RemoveCourseFromDegreeRequest {
  int64 degree_id = 1;
  int64 course_id = 2;
}

message GetCoursesByDegreeRequest {
  int64 degree_id = 1;
}

message GetCoursesForStudentRequest {
  int64 user_id = 1;
}

message DegreeCourseResponse {
  int64 id = 1;
  int64 degree_id = 2;
  int64 course_id = 3;
  bool is_required = 4;
  int32 semester_number = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

//-------------------------------//
//       Empty Messages          //
//-------------------------------//
message EmptyRequest {}

message EmptyResponse {}