#!/usr/bin/env python3
import requests
import json

BASE_URL = "http://localhost:8081"

def create_admin():
    """Создать админа"""
    data = {
        "name": "Admin",
        "surname": "User", 
        "email": "<EMAIL>",
        "username": "admin",
        "password": "admin123",
        "role": "admin"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=data)
    print(f"Create admin: {response.status_code}")
    if response.status_code == 201:
        print(f"Admin created: {response.json()}")
        return response.json()
    else:
        print(f"Error: {response.text}")
        return None

def login_admin():
    """Войти как админ"""
    data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=data)
    print(f"Admin login: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Admin token: {result.get('access_token', 'No token')}")
        return result.get('access_token')
    else:
        print(f"Error: {response.text}")
        return None

def create_student():
    """Создать студента"""
    data = {
        "name": "Student",
        "surname": "Test",
        "email": "<EMAIL>", 
        "username": "student",
        "password": "student123",
        "role": "student"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=data)
    print(f"Create student: {response.status_code}")
    if response.status_code == 201:
        result = response.json()
        print(f"Student created: {result}")
        return result
    else:
        print(f"Error: {response.text}")
        return None

def login_student():
    """Войти как студент"""
    data = {
        "username": "student",
        "password": "student123"
    }

    response = requests.post(f"{BASE_URL}/auth/login", json=data)
    print(f"Student login: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Student token: {result.get('access_token', 'No token')}")
        # Извлекаем user_id из объекта user
        user_id = result.get('user', {}).get('id')
        print(f"Student user_id: {user_id}")
        return result.get('access_token'), user_id
    else:
        print(f"Error: {response.text}")
        return None, None

def add_course_to_degree(admin_token, degree_id, course_id, semester_number=1):
    """Добавить курс к программе"""
    headers = {"Authorization": f"Bearer {admin_token}"}
    data = {
        "degree_id": degree_id,
        "course_id": course_id,
        "is_required": True,
        "semester_number": semester_number
    }
    
    response = requests.post(f"{BASE_URL}/degree-courses", json=data, headers=headers)
    print(f"Add course {course_id} to degree {degree_id}: {response.status_code}")
    if response.status_code == 201:
        print(f"Course added: {response.json()}")
        return True
    else:
        print(f"Error: {response.text}")
        return False

def assign_student_to_degree(admin_token, user_id, degree_id):
    """Назначить студента на программу"""
    headers = {"Authorization": f"Bearer {admin_token}"}
    data = {
        "user_id": user_id,
        "degree_id": degree_id,
        "start_date": "2024-09-01"
    }
    
    response = requests.post(f"{BASE_URL}/student-degrees", json=data, headers=headers)
    print(f"Assign student {user_id} to degree {degree_id}: {response.status_code}")
    if response.status_code == 201:
        print(f"Student assigned: {response.json()}")
        return True
    else:
        print(f"Error: {response.text}")
        return False

def get_student_courses(student_token, user_id):
    """Получить курсы студента"""
    headers = {"Authorization": f"Bearer {student_token}"}
    
    response = requests.get(f"{BASE_URL}/students/{user_id}/available-courses", headers=headers)
    print(f"Get student courses: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Student courses: {result}")
        return result
    else:
        print(f"Error: {response.text}")
        return None

def get_degree_courses(degree_id):
    """Получить курсы программы"""
    response = requests.get(f"{BASE_URL}/degrees/{degree_id}/courses")
    print(f"Get degree courses: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Degree courses: {result}")
        return result
    else:
        print(f"Error: {response.text}")
        return None

def main():
    print("=== Тестирование системы управления программами и курсами ===\n")
    
    # 1. Создать админа
    print("1. Создание админа...")
    admin_user = create_admin()
    
    # 2. Войти как админ
    print("\n2. Вход как админ...")
    admin_token = login_admin()
    if not admin_token:
        print("Не удалось получить токен админа")
        return
    
    # 3. Создать студента
    print("\n3. Создание студента...")
    student_user = create_student()
    
    # 4. Войти как студент
    print("\n4. Вход как студент...")
    student_token, student_user_id = login_student()
    if not student_token:
        print("Не удалось получить токен студента")
        return
    
    # 5. Привязать курсы к программе Computer Science (degree_id = 1)
    print("\n5. Привязка курсов к программе Computer Science...")
    add_course_to_degree(admin_token, 1, 1, 1)  # Golang - семестр 1
    add_course_to_degree(admin_token, 1, 2, 2)  # Golang Advanced - семестр 2
    
    # 6. Назначить студента на программу
    print("\n6. Назначение студента на программу...")
    assign_student_to_degree(admin_token, student_user_id, 1)
    
    # 7. Проверить курсы студента
    print("\n7. Проверка курсов студента...")
    get_student_courses(student_token, student_user_id)
    
    # 8. Проверить курсы программы
    print("\n8. Проверка курсов программы...")
    get_degree_courses(1)
    
    print("\n=== Тестирование завершено ===")

if __name__ == "__main__":
    main()
