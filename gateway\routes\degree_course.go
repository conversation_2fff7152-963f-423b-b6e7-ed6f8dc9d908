package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupDegreeCourseRoutes sets up routes for degree-course management
func SetupDegreeCourseRoutes(r *gin.Engine, authClient *clients.AuthClient, degreeCourseHandler *handlers.DegreeCourseHandler) {
	// Temporary: Public routes for testing (remove auth requirement)
	publicGroup := r.Group("/degree-courses")
	{
		// Add course to degree
		publicGroup.POST("", degreeCourseHandler.AddCourseToDegree)

		// Remove course from degree
		publicGroup.DELETE("/degree/:degree_id/course/:course_id", degreeCourseHandler.RemoveCourseFromDegree)

		// Get courses by degree
		publicGroup.GET("/degree/:degree_id/courses", degreeCourseHandler.GetCoursesByDegree)
	}

	// Student routes (also public for testing)
	studentGroup := r.Group("/students")
	{
		// Get courses available to a specific student based on their degree
		studentGroup.GET("/:user_id/available-courses", degreeCourseHandler.GetCoursesForStudent)
	}
}
