package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupDegreeCourseRoutes sets up routes for degree-course management
func SetupDegreeCourseRoutes(r *gin.Engine, authClient *clients.AuthClient, degreeCourseHandler *handlers.DegreeCourseHandler) {
	// Protected routes (require authentication)
	protected := r.Group("/")
	protected.Use(AuthMiddleware(authClient))
	{
		// Admin-only routes for managing degree-course relationships
		adminGroup := protected.Group("/degree-courses")
		adminGroup.Use(RoleMiddleware(authClient, "admin"))
		{
			// Add course to degree
			adminGroup.POST("", degreeCourseHandler.AddCourseToDegree)

			// Remove course from degree
			adminGroup.DELETE("/degree/:degree_id/course/:course_id", degreeCourseHandler.RemoveCourseFromDegree)

			// Get courses by degree (admin can see all)
			adminGroup.GET("/degree/:degree_id/courses", degreeCourseHandler.GetCoursesByDegree)
		}

		// Student routes (temporary: no auth for testing)
		// studentGroup := protected.Group("/students")
		// {
		// 	// Get courses available to a specific student based on their degree
		// 	studentGroup.GET("/:user_id/available-courses", degreeCourseHandler.GetCoursesForStudent)
		// }
	}

	// Temporary: Public endpoints for testing
	r.GET("/test-students/:user_id/available-courses", degreeCourseHandler.GetCoursesForStudent)
	r.GET("/students/:user_id/available-courses", degreeCourseHandler.GetCoursesForStudent)
}
