# Degree-Courses API Documentation

## Обзор

API для управления связями между программами обучения (degrees) и курсами. Позволяет администраторам настраивать, какие курсы доступны в рамках определенных программ обучения, а студентам - получать список курсов, доступных в их программе.

## Аутентификация

Все эндпоинты требуют аутентификации через Bearer токен в заголовке `Authorization`.

## Эндпоинты

### 1. Добавить курс к программе

**POST** `/degree-courses`

Добавляет курс к программе обучения. Доступно только администраторам.

#### Заголовки
```
Authorization: Bearer <token>
Content-Type: application/json
```

#### Тело запроса
```json
{
  "degree_id": 1,
  "course_id": 2,
  "is_required": true,
  "semester_number": 1
}
```

#### Параметры
- `degree_id` (int64, обязательно) - ID программы обучения
- `course_id` (int64, обязательно) - ID курса
- `is_required` (bool, опционально) - Является ли курс обязательным (по умолчанию false)
- `semester_number` (int32, опционально) - Номер семестра

#### Ответ
**Статус:** 201 Created
```json
{
  "id": 1,
  "degree_id": 1,
  "course_id": 2,
  "is_required": true,
  "semester_number": 1,
  "created_at": {
    "seconds": 1749739135,
    "nanos": 975903000
  },
  "updated_at": {
    "seconds": 1749739135,
    "nanos": 975903000
  }
}
```

#### Ошибки
- `400 Bad Request` - Неверные данные запроса
- `401 Unauthorized` - Отсутствует или неверный токен
- `403 Forbidden` - Недостаточно прав (не администратор)
- `500 Internal Server Error` - Внутренняя ошибка сервера

---

### 2. Удалить курс из программы

**DELETE** `/degree-courses/degree/{degree_id}/course/{course_id}`

Удаляет курс из программы обучения. Доступно только администраторам.

#### Заголовки
```
Authorization: Bearer <token>
```

#### Параметры пути
- `degree_id` (int64) - ID программы обучения
- `course_id` (int64) - ID курса

#### Ответ
**Статус:** 200 OK
```json
{
  "message": "Course removed from degree successfully"
}
```

#### Ошибки
- `400 Bad Request` - Неверные параметры
- `401 Unauthorized` - Отсутствует или неверный токен
- `403 Forbidden` - Недостаточно прав (не администратор)
- `404 Not Found` - Связь не найдена
- `500 Internal Server Error` - Внутренняя ошибка сервера

---

### 3. Получить курсы программы

**GET** `/degree-courses/degree/{degree_id}/courses`

Возвращает все курсы, связанные с определенной программой обучения. Доступно только администраторам.

#### Заголовки
```
Authorization: Bearer <token>
```

#### Параметры пути
- `degree_id` (int64) - ID программы обучения

#### Ответ
**Статус:** 200 OK
```json
{
  "courses": [
    {
      "id": 1,
      "title": "Golang",
      "description": "Golang course provides a comprehensive introduction...",
      "banner_image_url": "course-images/golang.jpg",
      "created_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      },
      "updated_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      }
    }
  ]
}
```

#### Ошибки
- `400 Bad Request` - Неверный ID программы
- `401 Unauthorized` - Отсутствует или неверный токен
- `403 Forbidden` - Недостаточно прав (не администратор)
- `500 Internal Server Error` - Внутренняя ошибка сервера

---

### 4. Получить доступные курсы для студента

**GET** `/students/{user_id}/available-courses`

Возвращает курсы, доступные студенту на основе его программы обучения. Доступно аутентифицированным пользователям.

#### Заголовки
```
Authorization: Bearer <token>
```

#### Параметры пути
- `user_id` (int64) - ID пользователя (студента)

#### Ответ
**Статус:** 200 OK
```json
{
  "courses": [
    {
      "id": 1,
      "title": "Available Course",
      "description": "Course available to student",
      "banner_image_url": "course-images/available.jpg",
      "created_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      },
      "updated_at": {
        "seconds": 1749739135,
        "nanos": 975903000
      }
    }
  ]
}
```

#### Ошибки
- `400 Bad Request` - Неверный ID пользователя
- `401 Unauthorized` - Отсутствует или неверный токен
- `500 Internal Server Error` - Внутренняя ошибка сервера

## Примеры использования

### Добавление курса к программе
```bash
curl -X POST http://localhost:8081/degree-courses \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "degree_id": 1,
    "course_id": 2,
    "is_required": true,
    "semester_number": 1
  }'
```

### Получение курсов программы
```bash
curl -X GET http://localhost:8081/degree-courses/degree/1/courses \
  -H "Authorization: Bearer <admin_token>"
```

### Получение доступных курсов для студента
```bash
curl -X GET http://localhost:8081/students/123/available-courses \
  -H "Authorization: Bearer <student_token>"
```

### Удаление курса из программы
```bash
curl -X DELETE http://localhost:8081/degree-courses/degree/1/course/2 \
  -H "Authorization: Bearer <admin_token>"
```

## Архитектура

Система degree-courses реализует контроль доступа к курсам на основе программ обучения:

1. **Администраторы** могут управлять связями между программами и курсами
2. **Студенты** видят только курсы, доступные в рамках их программы обучения
3. **Система** автоматически фильтрует курсы на основе назначенной студенту программы

## База данных

### Таблица degree_courses
```sql
CREATE TABLE degree_courses (
    id BIGSERIAL PRIMARY KEY,
    degree_id BIGINT NOT NULL REFERENCES degrees(id),
    course_id BIGINT NOT NULL REFERENCES courses(id),
    is_required BOOLEAN DEFAULT FALSE,
    semester_number INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(degree_id, course_id)
);
```

### Связанные таблицы
- `degrees` - программы обучения
- `courses` - курсы
- `student_degrees` - назначение программ студентам

## Безопасность

- Все эндпоинты требуют аутентификации
- Управление связями доступно только администраторам
- Студенты могут видеть только свои доступные курсы
- Валидация входных данных на всех уровнях
