package handlers

import (
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// StudentHandler handles student-related requests
type StudentHandler struct {
	ThreadClient       *clients.ThreadClient
	ScheduleClient     *clients.ScheduleClient
	SportClient        *clients.ScheduleClient
	BookingClient      *clients.BookingClient
	AssignmentClient   *clients.AssignmentClient
	RabbitLogPublisher clients.LogPublisher
}

// NewStudentHandler creates a new StudentHandler
func NewStudentHandler(
	threadClient *clients.ThreadClient,
	scheduleClient *clients.ScheduleClient,
	sportClient *clients.ScheduleClient,
	bookingClient *clients.BookingClient,
	assignmentClient *clients.AssignmentClient,
	rabbitLogPublisher clients.LogPublisher,
) *StudentHandler {
	return &StudentHandler{
		ThreadClient:       threadClient,
		ScheduleClient:     scheduleClient,
		SportClient:        sportClient,
		BookingClient:      bookingClient,
		AssignmentClient:   assignmentClient,
		RabbitLogPublisher: rabbitLogPublisher,
	}
}

// GetStudentScheduleHandler handles GET /students/:student_id/schedule
// This endpoint returns a comprehensive schedule for a student, including:
// - Thread schedules (regular class times)
// - Sport schedules and bookings
func (h *StudentHandler) GetStudentScheduleHandler(c *gin.Context) {
	// Parse student ID
	studentIDStr := c.Param("user_id")
	studentID, err := strconv.ParseInt(studentIDStr, 10, 64)
	if err != nil || studentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid student ID"})
		return
	}

	// Parse optional date range parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	if startDateStr != "" {
		parsedStartDate, err := time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid start_date format, use RFC3339"})
			return
		}
		startDate = parsedStartDate
	} else {
		// Default to today
		startDate = time.Now().Truncate(24 * time.Hour)
	}

	if endDateStr != "" {
		parsedEndDate, err := time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid end_date format, use RFC3339"})
			return
		}
		endDate = parsedEndDate
	} else {
		// Default to 7 days from start date
		endDate = startDate.AddDate(0, 0, 7)
	}

	// 1. Get threads the student is registered for
	threads, err := h.ThreadClient.ListThreadsForUser(c.Request.Context(), &threadpb.UserThreadsRequest{
		UserId: studentID,
	})
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting threads for student: %v", err),
			"student_schedule",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get student threads"})
		return
	}

	// 2. Get thread schedules for each thread
	var threadSchedules []gin.H
	for _, thread := range threads.GetThreads() {
		schedules, err := h.ThreadClient.ListThreadSchedules(c.Request.Context(), &threadpb.ThreadSchedulesRequest{
			ThreadId: thread.Thread.Id,
		})
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting schedules for thread: %v", err),
				"student_schedule",
				map[string]any{"thread_id": thread.Thread.Id, "error": err.Error()},
			)
			continue // Skip this thread but continue with others
		}

		// Convert weekly schedules to actual dates within the range
		for _, schedule := range schedules.GetSchedules() {
			// Generate actual dates for this schedule within the date range
			for date := startDate; date.Before(endDate); date = date.AddDate(0, 0, 1) {
				// Check if this date's day of week matches the schedule
				if int(date.Weekday()) == int(schedule.DayOfWeek)%7 {
					// Parse the start and end times
					startTime, _ := time.Parse("15:04:05", schedule.StartTime)
					endTime, _ := time.Parse("15:04:05", schedule.EndTime)

					// Combine date with time
					scheduleStart := time.Date(
						date.Year(), date.Month(), date.Day(),
						startTime.Hour(), startTime.Minute(), startTime.Second(),
						0, date.Location(),
					)
					scheduleEnd := time.Date(
						date.Year(), date.Month(), date.Day(),
						endTime.Hour(), endTime.Minute(), endTime.Second(),
						0, date.Location(),
					)

					// Get location information
					var location string
					var locationID int64

					// First try to get location from schedule
					location = schedule.Location

					// If location is empty and we have a location_id, try to get location by ID
					if location == "" && schedule.Id > 0 {
						// Get the schedule by ID to check if it has location information
						scheduleResp, err := h.ThreadClient.GetThreadScheduleByID(c.Request.Context(), &threadpb.ThreadScheduleByID{
							ScheduleId: schedule.Id,
						})
						if err == nil && scheduleResp != nil {
							location = scheduleResp.Location

							// If we have a schedule but still no location, try to get location from location service
							if location == "" {
								// Try to get location from the thread service using the schedule ID
								locationFromService, err := h.ThreadClient.GetScheduleLocation(c.Request.Context(), schedule.Id)
								if err == nil && locationFromService != "" {
									location = locationFromService
								}
							}
						}
					}

					// If still no location, provide a default message
					if location == "" {
						location = "No location specified"
					}

					threadSchedules = append(threadSchedules, gin.H{
						"type":        "class",
						"thread_id":   thread.Thread.Id,
						"thread_name": thread.Thread.Title,
						"course_name": thread.Course.Title,
						"start_time":  scheduleStart.Format(time.RFC3339),
						"end_time":    scheduleEnd.Format(time.RFC3339),
						"location":    location,
						"schedule_id": schedule.Id,
						"location_id": locationID,
					})
				}
			}
		}
	}

	// 3. Get sport bookings for the student
	startTimestamp := timestamppb.New(startDate)
	endTimestamp := timestamppb.New(endDate)

	bookings, err := h.BookingClient.ListBookingsByUser(c.Request.Context(), studentID, startTimestamp, endTimestamp)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting sport bookings for student: %v", err),
			"student_schedule",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		// Continue without sport bookings
	}

	// 4. Convert sport bookings to schedule items
	var sportSchedules []gin.H
	if bookings != nil {
		for _, booking := range bookings.GetBookings() {
			sportSchedules = append(sportSchedules, gin.H{
				"type":        "sport",
				"booking_id":  booking.Id,
				"schedule_id": booking.ScheduleId,
				"start_time":  booking.Schedule.StartTime.AsTime().Format(time.RFC3339),
				"end_time":    booking.Schedule.EndTime.AsTime().Format(time.RFC3339),
				"location":    booking.Schedule.Location,
				"sport_type":  "Sport Activity", // We don't have sport type in the schedule info
				"status":      booking.Status,
			})
		}
	}

	// 5. Combine all schedules and sort by start time
	allSchedules := append(threadSchedules, sportSchedules...)

	// Sort schedules by start time
	sort.Slice(allSchedules, func(i, j int) bool {
		startTime1, _ := time.Parse(time.RFC3339, allSchedules[i]["start_time"].(string))
		startTime2, _ := time.Parse(time.RFC3339, allSchedules[j]["start_time"].(string))
		return startTime1.Before(startTime2)
	})

	c.JSON(http.StatusOK, gin.H{
		"student_id": studentID,
		"start_date": startDate.Format(time.RFC3339),
		"end_date":   endDate.Format(time.RFC3339),
		"schedules":  allSchedules,
	})
}

// ListPendingAssignmentsHandler возвращает все задания, которые студент еще не выполнил (нет сабмишенов)
func (h *StudentHandler) ListPendingAssignmentsHandler(c *gin.Context) {
	// Parse student ID
	studentIDStr := c.Param("user_id")
	studentID, err := strconv.ParseInt(studentIDStr, 10, 64)
	if err != nil || studentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid student ID"})
		return
	}

	// Parse thread ID
	threadIDStr := c.Param("thread_id")
	threadID, err := strconv.ParseInt(threadIDStr, 10, 64)
	if err != nil || threadID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread ID"})
		return
	}

	// Get assignments without submissions
	assignments, err := h.AssignmentClient.ListAssignmentsWithoutSubmissionForThread(
		c.Request.Context(),
		threadID,
		studentID,
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting pending assignments: %v", err),
			"pending_assignments",
			map[string]any{
				"student_id": studentID,
				"thread_id":  threadID,
				"error":      err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get pending assignments"})
		return
	}

	// Format the response
	var formattedAssignments []gin.H
	for _, item := range assignments {
		assignment := item.GetAssignment()

		// Calculate deadline status
		now := time.Now()
		dueDate := assignment.GetDueDate().AsTime()
		daysRemaining := int(dueDate.Sub(now).Hours() / 24)
		isOverdue := now.After(dueDate)

		var statusText string
		if isOverdue {
			statusText = "Overdue"
		} else if daysRemaining == 0 {
			statusText = "Due today"
		} else if daysRemaining == 1 {
			statusText = "Due tomorrow"
		} else {
			statusText = "Due in " + strconv.Itoa(daysRemaining) + " days"
		}

		formattedAssignments = append(formattedAssignments, gin.H{
			"id":                  assignment.GetId(),
			"week_id":             assignment.GetWeekId(),
			"title":               assignment.GetTitle(),
			"description":         assignment.GetDescription(),
			"due_date":            assignment.GetDueDate().AsTime().Format(time.RFC3339),
			"max_points":          assignment.GetMaxPoints(),
			"assignment_group_id": assignment.GetAssignmentGroupId(),
			"type":                assignment.GetType(),
			"created_at":          assignment.GetCreatedAt().AsTime().Format(time.RFC3339),
			"updated_at":          assignment.GetUpdatedAt().AsTime().Format(time.RFC3339),
			"deadline_status": gin.H{
				"is_overdue":     isOverdue,
				"days_remaining": daysRemaining,
				"status_text":    statusText,
			},
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"student_id":  studentID,
		"thread_id":   threadID,
		"count":       len(formattedAssignments),
		"assignments": formattedAssignments,
	})
}

// ListAllPendingAssignmentsHandler возвращает все задания из всех потоков, которые студент еще не выполнил
func (h *StudentHandler) ListAllPendingAssignmentsHandler(c *gin.Context) {
	// Parse student ID
	studentIDStr := c.Param("user_id")
	studentID, err := strconv.ParseInt(studentIDStr, 10, 64)
	if err != nil || studentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid student ID"})
		return
	}

	// Get all threads the student is enrolled in
	threadsResp, err := h.ThreadClient.ListThreadsForUser(
		c.Request.Context(),
		&threadpb.UserThreadsRequest{UserId: studentID},
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting threads for student: %v", err),
			"all_pending_assignments",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get student threads"})
		return
	}

	if len(threadsResp.GetThreads()) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"student_id":  studentID,
			"count":       0,
			"assignments": []any{},
		})
		return
	}

	// For each thread, get assignments without submissions
	var allPendingAssignments []gin.H
	threadInfo := make(map[int64]gin.H)

	for _, threadWithDetails := range threadsResp.GetThreads() {
		thread := threadWithDetails.GetThread()
		course := threadWithDetails.GetCourse()

		// Store thread info for reference
		threadInfo[thread.Id] = gin.H{
			"id":    thread.Id,
			"title": thread.Title,
			"course": gin.H{
				"id":    course.Id,
				"title": course.Title,
			},
		}

		// Get assignments without submissions for this thread
		assignments, err := h.AssignmentClient.ListAssignmentsWithoutSubmissionForThread(
			c.Request.Context(),
			thread.Id,
			studentID,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting pending assignments for thread: %v", err),
				"all_pending_assignments",
				map[string]any{
					"student_id": studentID,
					"thread_id":  thread.Id,
					"error":      err.Error(),
				},
			)
			// Continue with other threads even if this one fails
			continue
		}

		// Format and add assignments to the combined list
		for _, item := range assignments {
			assignment := item.GetAssignment()

			// Calculate deadline status
			now := time.Now()
			dueDate := assignment.GetDueDate().AsTime()
			daysRemaining := int(dueDate.Sub(now).Hours() / 24)
			isOverdue := now.After(dueDate)

			var statusText string
			if isOverdue {
				statusText = "Overdue"
			} else if daysRemaining == 0 {
				statusText = "Due today"
			} else if daysRemaining == 1 {
				statusText = "Due tomorrow"
			} else {
				statusText = "Due in " + strconv.Itoa(daysRemaining) + " days"
			}

			allPendingAssignments = append(allPendingAssignments, gin.H{
				"id":                  assignment.GetId(),
				"week_id":             assignment.GetWeekId(),
				"title":               assignment.GetTitle(),
				"description":         assignment.GetDescription(),
				"due_date":            assignment.GetDueDate().AsTime().Format(time.RFC3339),
				"max_points":          assignment.GetMaxPoints(),
				"assignment_group_id": assignment.GetAssignmentGroupId(),
				"type":                assignment.GetType(),
				"created_at":          assignment.GetCreatedAt().AsTime().Format(time.RFC3339),
				"updated_at":          assignment.GetUpdatedAt().AsTime().Format(time.RFC3339),
				"thread":              threadInfo[thread.Id],
				"deadline_status": gin.H{
					"is_overdue":     isOverdue,
					"days_remaining": daysRemaining,
					"status_text":    statusText,
				},
			})
		}
	}

	// Sort assignments by due date (closest deadline first)
	sort.Slice(allPendingAssignments, func(i, j int) bool {
		dueDate1, _ := time.Parse(time.RFC3339, allPendingAssignments[i]["due_date"].(string))
		dueDate2, _ := time.Parse(time.RFC3339, allPendingAssignments[j]["due_date"].(string))
		return dueDate1.Before(dueDate2)
	})

	c.JSON(http.StatusOK, gin.H{
		"student_id":  studentID,
		"count":       len(allPendingAssignments),
		"assignments": allPendingAssignments,
	})
}
