# Система управления программами и курсами

## Обзор

Реализована система, где:
1. **Программы (degrees)** - образовательные программы (бакалавриат, магистратура и т.д.)
2. **Курсы привязываются к программам** - каждый курс может быть частью одной или нескольких программ
3. **Студенты видят только курсы своей программы** - доступ ограничен на основе программы студента
4. **Регистрация на курсы ограничена** - студенты могут регистрироваться только на курсы своей программы

## Структура базы данных

### Новая таблица: degree_courses
```sql
CREATE TABLE degree_courses (
    id BIGSERIAL PRIMARY KEY,
    degree_id BIGINT NOT NULL REFERENCES degrees(id) ON DELETE CASCADE,
    course_id BIGINT NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    is_required BOOLEAN DEFAULT TRUE, -- Обязательный или элективный курс
    semester_number INT, -- Рекомендуемый семестр
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(degree_id, course_id)
);
```

### Существующие таблицы
- `degrees` - программы обучения
- `student_degrees` - связь студентов с программами
- `courses` - курсы
- `threads` - потоки курсов

## API Эндпоинты

### Управление связями курсов и программ (только для админов)

#### 1. Добавить курс к программе
```http
POST /degree-courses
Authorization: Bearer <admin_token>
Content-Type: application/json

{
    "degree_id": 1,
    "course_id": 5,
    "is_required": true,
    "semester_number": 2
}
```

#### 2. Удалить курс из программы
```http
DELETE /degree-courses/degree/1/course/5
Authorization: Bearer <admin_token>
```

#### 3. Получить курсы программы
```http
GET /degree-courses/degree/1/courses
Authorization: Bearer <admin_token>
```

### Студентские эндпоинты

#### 4. Получить доступные курсы для студента
```http
GET /students/123/available-courses
Authorization: Bearer <student_token>
```

### Публичные эндпоинты

#### 5. Просмотр курсов программы (публичный доступ)
```http
GET /degrees/1/courses
Authorization: Bearer <any_token>
```

## Логика работы

### 1. Регистрация на курсы
При попытке регистрации на thread (поток курса), система проверяет:
1. **Доступ к курсу** - есть ли у студента доступ к курсу на основе его программы
2. **Пререквизиты** - выполнены ли все предварительные требования
3. **Свободные места** - есть ли места в потоке

### 2. Просмотр курсов
- **Студенты** видят только курсы своей программы через `/students/{id}/available-courses`
- **Админы** могут видеть все курсы и управлять связями
- **Публичный доступ** к просмотру курсов программы

### 3. Проверка доступа
Новый метод `CheckCourseAccessForStudent` проверяет:
```sql
SELECT EXISTS(
    SELECT 1
    FROM degree_courses dc
    INNER JOIN student_degrees sd ON dc.degree_id = sd.degree_id
    WHERE sd.user_id = $1 AND dc.course_id = $2 AND sd.status = 'in_progress'
)
```

## Примеры использования

### Настройка программы Computer Science
```bash
# 1. Создать программу (если еще не создана)
curl -X POST http://localhost:8081/degrees \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Bachelor of Computer Science",
    "level": "bachelor",
    "required_credits": 120
  }'

# 2. Добавить курсы к программе
curl -X POST http://localhost:8081/degree-courses \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "degree_id": 1,
    "course_id": 1,
    "is_required": true,
    "semester_number": 1
  }'

# 3. Назначить студента на программу
curl -X POST http://localhost:8081/student-degrees \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123,
    "degree_id": 1,
    "start_date": "2024-09-01"
  }'
```

### Студент просматривает доступные курсы
```bash
curl -X GET http://localhost:8081/students/123/available-courses \
  -H "Authorization: Bearer <student_token>"
```

### Студент пытается зарегистрироваться на курс
```bash
# Это будет работать только если курс принадлежит программе студента
curl -X POST http://localhost:8081/thread_registrations/register \
  -H "Authorization: Bearer <student_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123,
    "thread_id": 456
  }'
```

## Безопасность

1. **Авторизация** - все эндпоинты требуют аутентификации
2. **Роли** - управление связями доступно только админам
3. **Проверка доступа** - автоматическая проверка при регистрации на курсы
4. **Изоляция данных** - студенты видят только свои курсы

## Преимущества системы

1. **Структурированность** - четкая организация курсов по программам
2. **Безопасность** - студенты не могут регистрироваться на чужие курсы
3. **Гибкость** - курсы могут быть частью нескольких программ
4. **Масштабируемость** - легко добавлять новые программы и курсы
5. **Контроль** - админы полностью контролируют доступ к курсам
