// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pb/thread/thread.proto

package threadpb

import (
	assignment "github.com/olzzhas/edunite-server/course_service/pb/assignment"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ========================================================
//
//	THREAD core
//
// ========================================================
type ThreadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CourseId      int64                  `protobuf:"varint,1,opt,name=course_id,json=courseId,proto3" json:"course_id,omitempty"`
	SemesterId    int64                  `protobuf:"varint,2,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	TeacherId     int64                  `protobuf:"varint,3,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	Title         string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	SyllabusUrl   string                 `protobuf:"bytes,5,opt,name=syllabus_url,json=syllabusUrl,proto3" json:"syllabus_url,omitempty"`
	MaxStudents   int32                  `protobuf:"varint,6,opt,name=max_students,json=maxStudents,proto3" json:"max_students,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadRequest) Reset() {
	*x = ThreadRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadRequest) ProtoMessage() {}

func (x *ThreadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadRequest.ProtoReflect.Descriptor instead.
func (*ThreadRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{0}
}

func (x *ThreadRequest) GetCourseId() int64 {
	if x != nil {
		return x.CourseId
	}
	return 0
}

func (x *ThreadRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *ThreadRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *ThreadRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ThreadRequest) GetSyllabusUrl() string {
	if x != nil {
		return x.SyllabusUrl
	}
	return ""
}

func (x *ThreadRequest) GetMaxStudents() int32 {
	if x != nil {
		return x.MaxStudents
	}
	return 0
}

type ThreadUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CourseId      int64                  `protobuf:"varint,2,opt,name=course_id,json=courseId,proto3" json:"course_id,omitempty"`
	SemesterId    int64                  `protobuf:"varint,3,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	TeacherId     int64                  `protobuf:"varint,4,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	Title         string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	SyllabusUrl   string                 `protobuf:"bytes,6,opt,name=syllabus_url,json=syllabusUrl,proto3" json:"syllabus_url,omitempty"`
	MaxStudents   int32                  `protobuf:"varint,7,opt,name=max_students,json=maxStudents,proto3" json:"max_students,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadUpdateRequest) Reset() {
	*x = ThreadUpdateRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadUpdateRequest) ProtoMessage() {}

func (x *ThreadUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadUpdateRequest.ProtoReflect.Descriptor instead.
func (*ThreadUpdateRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{1}
}

func (x *ThreadUpdateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ThreadUpdateRequest) GetCourseId() int64 {
	if x != nil {
		return x.CourseId
	}
	return 0
}

func (x *ThreadUpdateRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *ThreadUpdateRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *ThreadUpdateRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ThreadUpdateRequest) GetSyllabusUrl() string {
	if x != nil {
		return x.SyllabusUrl
	}
	return ""
}

func (x *ThreadUpdateRequest) GetMaxStudents() int32 {
	if x != nil {
		return x.MaxStudents
	}
	return 0
}

type ThreadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CourseId      int64                  `protobuf:"varint,2,opt,name=course_id,json=courseId,proto3" json:"course_id,omitempty"`
	SemesterId    int64                  `protobuf:"varint,3,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	TeacherId     int64                  `protobuf:"varint,4,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	Title         string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	SyllabusUrl   string                 `protobuf:"bytes,6,opt,name=syllabus_url,json=syllabusUrl,proto3" json:"syllabus_url,omitempty"`
	MaxStudents   int32                  `protobuf:"varint,7,opt,name=max_students,json=maxStudents,proto3" json:"max_students,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadResponse) Reset() {
	*x = ThreadResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadResponse) ProtoMessage() {}

func (x *ThreadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadResponse.ProtoReflect.Descriptor instead.
func (*ThreadResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{2}
}

func (x *ThreadResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ThreadResponse) GetCourseId() int64 {
	if x != nil {
		return x.CourseId
	}
	return 0
}

func (x *ThreadResponse) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *ThreadResponse) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *ThreadResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ThreadResponse) GetSyllabusUrl() string {
	if x != nil {
		return x.SyllabusUrl
	}
	return ""
}

func (x *ThreadResponse) GetMaxStudents() int32 {
	if x != nil {
		return x.MaxStudents
	}
	return 0
}

func (x *ThreadResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ThreadResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type ThreadByID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadByID) Reset() {
	*x = ThreadByID{}
	mi := &file_pb_thread_thread_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadByID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadByID) ProtoMessage() {}

func (x *ThreadByID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadByID.ProtoReflect.Descriptor instead.
func (*ThreadByID) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{3}
}

func (x *ThreadByID) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

type ThreadsByCourseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CourseId      int64                  `protobuf:"varint,1,opt,name=course_id,json=courseId,proto3" json:"course_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadsByCourseRequest) Reset() {
	*x = ThreadsByCourseRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadsByCourseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadsByCourseRequest) ProtoMessage() {}

func (x *ThreadsByCourseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadsByCourseRequest.ProtoReflect.Descriptor instead.
func (*ThreadsByCourseRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{4}
}

func (x *ThreadsByCourseRequest) GetCourseId() int64 {
	if x != nil {
		return x.CourseId
	}
	return 0
}

type ThreadsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Threads       []*ThreadResponse      `protobuf:"bytes,1,rep,name=threads,proto3" json:"threads,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadsResponse) Reset() {
	*x = ThreadsResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadsResponse) ProtoMessage() {}

func (x *ThreadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadsResponse.ProtoReflect.Descriptor instead.
func (*ThreadsResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{5}
}

func (x *ThreadsResponse) GetThreads() []*ThreadResponse {
	if x != nil {
		return x.Threads
	}
	return nil
}

type ThreadEmptyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadEmptyRequest) Reset() {
	*x = ThreadEmptyRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadEmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadEmptyRequest) ProtoMessage() {}

func (x *ThreadEmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadEmptyRequest.ProtoReflect.Descriptor instead.
func (*ThreadEmptyRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{6}
}

type ThreadEmptyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadEmptyResponse) Reset() {
	*x = ThreadEmptyResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadEmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadEmptyResponse) ProtoMessage() {}

func (x *ThreadEmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadEmptyResponse.ProtoReflect.Descriptor instead.
func (*ThreadEmptyResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{7}
}

// ========================================================
//
//	REGISTRATIONS
//
// ========================================================
type RegisterUserToThreadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterUserToThreadRequest) Reset() {
	*x = RegisterUserToThreadRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterUserToThreadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterUserToThreadRequest) ProtoMessage() {}

func (x *RegisterUserToThreadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterUserToThreadRequest.ProtoReflect.Descriptor instead.
func (*RegisterUserToThreadRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{8}
}

func (x *RegisterUserToThreadRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RegisterUserToThreadRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

type RegisterManyUsersToThreadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	UserId        []int64                `protobuf:"varint,2,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterManyUsersToThreadRequest) Reset() {
	*x = RegisterManyUsersToThreadRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterManyUsersToThreadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterManyUsersToThreadRequest) ProtoMessage() {}

func (x *RegisterManyUsersToThreadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterManyUsersToThreadRequest.ProtoReflect.Descriptor instead.
func (*RegisterManyUsersToThreadRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{9}
}

func (x *RegisterManyUsersToThreadRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *RegisterManyUsersToThreadRequest) GetUserId() []int64 {
	if x != nil {
		return x.UserId
	}
	return nil
}

type RemoveRegistrationToThreadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveRegistrationToThreadRequest) Reset() {
	*x = RemoveRegistrationToThreadRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveRegistrationToThreadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRegistrationToThreadRequest) ProtoMessage() {}

func (x *RemoveRegistrationToThreadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRegistrationToThreadRequest.ProtoReflect.Descriptor instead.
func (*RemoveRegistrationToThreadRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{10}
}

func (x *RemoveRegistrationToThreadRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RemoveRegistrationToThreadRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

type RemoveManyRegistrationsToThreadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	UserId        []int64                `protobuf:"varint,2,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveManyRegistrationsToThreadRequest) Reset() {
	*x = RemoveManyRegistrationsToThreadRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveManyRegistrationsToThreadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveManyRegistrationsToThreadRequest) ProtoMessage() {}

func (x *RemoveManyRegistrationsToThreadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveManyRegistrationsToThreadRequest.ProtoReflect.Descriptor instead.
func (*RemoveManyRegistrationsToThreadRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{11}
}

func (x *RemoveManyRegistrationsToThreadRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *RemoveManyRegistrationsToThreadRequest) GetUserId() []int64 {
	if x != nil {
		return x.UserId
	}
	return nil
}

type ThreadRegistrationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	FinalGrade    float32                `protobuf:"fixed32,3,opt,name=final_grade,json=finalGrade,proto3" json:"final_grade,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadRegistrationResponse) Reset() {
	*x = ThreadRegistrationResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadRegistrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadRegistrationResponse) ProtoMessage() {}

func (x *ThreadRegistrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadRegistrationResponse.ProtoReflect.Descriptor instead.
func (*ThreadRegistrationResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{12}
}

func (x *ThreadRegistrationResponse) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ThreadRegistrationResponse) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *ThreadRegistrationResponse) GetFinalGrade() float32 {
	if x != nil {
		return x.FinalGrade
	}
	return 0
}

type ListThreadRegistrationsResponse struct {
	state               protoimpl.MessageState        `protogen:"open.v1"`
	ThreadRegistrations []*ThreadRegistrationResponse `protobuf:"bytes,1,rep,name=thread_registrations,json=threadRegistrations,proto3" json:"thread_registrations,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ListThreadRegistrationsResponse) Reset() {
	*x = ListThreadRegistrationsResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListThreadRegistrationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListThreadRegistrationsResponse) ProtoMessage() {}

func (x *ListThreadRegistrationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListThreadRegistrationsResponse.ProtoReflect.Descriptor instead.
func (*ListThreadRegistrationsResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{13}
}

func (x *ListThreadRegistrationsResponse) GetThreadRegistrations() []*ThreadRegistrationResponse {
	if x != nil {
		return x.ThreadRegistrations
	}
	return nil
}

// Final grading for thread registration
type GradeFinalThreadRegistrationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	FinalGrade    float64                `protobuf:"fixed64,3,opt,name=final_grade,json=finalGrade,proto3" json:"final_grade,omitempty"` // 0-100 numeric grade
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GradeFinalThreadRegistrationRequest) Reset() {
	*x = GradeFinalThreadRegistrationRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GradeFinalThreadRegistrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GradeFinalThreadRegistrationRequest) ProtoMessage() {}

func (x *GradeFinalThreadRegistrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GradeFinalThreadRegistrationRequest.ProtoReflect.Descriptor instead.
func (*GradeFinalThreadRegistrationRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{14}
}

func (x *GradeFinalThreadRegistrationRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GradeFinalThreadRegistrationRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *GradeFinalThreadRegistrationRequest) GetFinalGrade() float64 {
	if x != nil {
		return x.FinalGrade
	}
	return 0
}

type GradeFinalThreadRegistrationResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Message       string                      `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Registration  *ThreadRegistrationResponse `protobuf:"bytes,2,opt,name=registration,proto3" json:"registration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GradeFinalThreadRegistrationResponse) Reset() {
	*x = GradeFinalThreadRegistrationResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GradeFinalThreadRegistrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GradeFinalThreadRegistrationResponse) ProtoMessage() {}

func (x *GradeFinalThreadRegistrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GradeFinalThreadRegistrationResponse.ProtoReflect.Descriptor instead.
func (*GradeFinalThreadRegistrationResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{15}
}

func (x *GradeFinalThreadRegistrationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GradeFinalThreadRegistrationResponse) GetRegistration() *ThreadRegistrationResponse {
	if x != nil {
		return x.Registration
	}
	return nil
}

// ========================================================
//
//	WEEK
//
// ========================================================
type WeekRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	WeekNumber    uint32                 `protobuf:"varint,2,opt,name=week_number,json=weekNumber,proto3" json:"week_number,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Title         string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WeekRequest) Reset() {
	*x = WeekRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeekRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeekRequest) ProtoMessage() {}

func (x *WeekRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeekRequest.ProtoReflect.Descriptor instead.
func (*WeekRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{16}
}

func (x *WeekRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *WeekRequest) GetWeekNumber() uint32 {
	if x != nil {
		return x.WeekNumber
	}
	return 0
}

func (x *WeekRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WeekRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *WeekRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type WeekUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WeekId        int64                  `protobuf:"varint,1,opt,name=week_id,json=weekId,proto3" json:"week_id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	WeekNumber    uint32                 `protobuf:"varint,3,opt,name=week_number,json=weekNumber,proto3" json:"week_number,omitempty"`
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Title         string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WeekUpdateRequest) Reset() {
	*x = WeekUpdateRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeekUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeekUpdateRequest) ProtoMessage() {}

func (x *WeekUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeekUpdateRequest.ProtoReflect.Descriptor instead.
func (*WeekUpdateRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{17}
}

func (x *WeekUpdateRequest) GetWeekId() int64 {
	if x != nil {
		return x.WeekId
	}
	return 0
}

func (x *WeekUpdateRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *WeekUpdateRequest) GetWeekNumber() uint32 {
	if x != nil {
		return x.WeekNumber
	}
	return 0
}

func (x *WeekUpdateRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WeekUpdateRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *WeekUpdateRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type WeekResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	WeekNumber    uint32                 `protobuf:"varint,3,opt,name=week_number,json=weekNumber,proto3" json:"week_number,omitempty"`
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Title         string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WeekResponse) Reset() {
	*x = WeekResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeekResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeekResponse) ProtoMessage() {}

func (x *WeekResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeekResponse.ProtoReflect.Descriptor instead.
func (*WeekResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{18}
}

func (x *WeekResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WeekResponse) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *WeekResponse) GetWeekNumber() uint32 {
	if x != nil {
		return x.WeekNumber
	}
	return 0
}

func (x *WeekResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WeekResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *WeekResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WeekResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *WeekResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type WeekByID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WeekId        int64                  `protobuf:"varint,1,opt,name=week_id,json=weekId,proto3" json:"week_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WeekByID) Reset() {
	*x = WeekByID{}
	mi := &file_pb_thread_thread_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeekByID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeekByID) ProtoMessage() {}

func (x *WeekByID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeekByID.ProtoReflect.Descriptor instead.
func (*WeekByID) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{19}
}

func (x *WeekByID) GetWeekId() int64 {
	if x != nil {
		return x.WeekId
	}
	return 0
}

type WeeksForThreadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WeeksForThreadRequest) Reset() {
	*x = WeeksForThreadRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeeksForThreadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeeksForThreadRequest) ProtoMessage() {}

func (x *WeeksForThreadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeeksForThreadRequest.ProtoReflect.Descriptor instead.
func (*WeeksForThreadRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{20}
}

func (x *WeeksForThreadRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

type WeeksResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Weeks         []*WeekResponse        `protobuf:"bytes,1,rep,name=weeks,proto3" json:"weeks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WeeksResponse) Reset() {
	*x = WeeksResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeeksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeeksResponse) ProtoMessage() {}

func (x *WeeksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeeksResponse.ProtoReflect.Descriptor instead.
func (*WeeksResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{21}
}

func (x *WeeksResponse) GetWeeks() []*WeekResponse {
	if x != nil {
		return x.Weeks
	}
	return nil
}

// ========================================================
//
//	SCHEDULE
//
// ========================================================
type ThreadScheduleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	DayOfWeek     uint32                 `protobuf:"varint,2,opt,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week,omitempty"` // 1=Mon … 7=Sun
	StartTime     string                 `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`    // "HH:MM:SS"
	EndTime       string                 `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`          // "HH:MM:SS"
	Location      string                 `protobuf:"bytes,5,opt,name=location,proto3" json:"location,omitempty"`                       // Cabinet/room name
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadScheduleRequest) Reset() {
	*x = ThreadScheduleRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadScheduleRequest) ProtoMessage() {}

func (x *ThreadScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadScheduleRequest.ProtoReflect.Descriptor instead.
func (*ThreadScheduleRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{22}
}

func (x *ThreadScheduleRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *ThreadScheduleRequest) GetDayOfWeek() uint32 {
	if x != nil {
		return x.DayOfWeek
	}
	return 0
}

func (x *ThreadScheduleRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ThreadScheduleRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ThreadScheduleRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

type ThreadScheduleUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	DayOfWeek     uint32                 `protobuf:"varint,3,opt,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week,omitempty"`
	StartTime     string                 `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string                 `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Location      string                 `protobuf:"bytes,6,opt,name=location,proto3" json:"location,omitempty"` // Cabinet/room name
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadScheduleUpdateRequest) Reset() {
	*x = ThreadScheduleUpdateRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadScheduleUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadScheduleUpdateRequest) ProtoMessage() {}

func (x *ThreadScheduleUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadScheduleUpdateRequest.ProtoReflect.Descriptor instead.
func (*ThreadScheduleUpdateRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{23}
}

func (x *ThreadScheduleUpdateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ThreadScheduleUpdateRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *ThreadScheduleUpdateRequest) GetDayOfWeek() uint32 {
	if x != nil {
		return x.DayOfWeek
	}
	return 0
}

func (x *ThreadScheduleUpdateRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ThreadScheduleUpdateRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ThreadScheduleUpdateRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

type ThreadScheduleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	DayOfWeek     uint32                 `protobuf:"varint,3,opt,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week,omitempty"`
	StartTime     string                 `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string                 `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Location      string                 `protobuf:"bytes,6,opt,name=location,proto3" json:"location,omitempty"` // Cabinet/room name
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadScheduleResponse) Reset() {
	*x = ThreadScheduleResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadScheduleResponse) ProtoMessage() {}

func (x *ThreadScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadScheduleResponse.ProtoReflect.Descriptor instead.
func (*ThreadScheduleResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{24}
}

func (x *ThreadScheduleResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ThreadScheduleResponse) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *ThreadScheduleResponse) GetDayOfWeek() uint32 {
	if x != nil {
		return x.DayOfWeek
	}
	return 0
}

func (x *ThreadScheduleResponse) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ThreadScheduleResponse) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ThreadScheduleResponse) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ThreadScheduleResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ThreadScheduleResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type ThreadScheduleByID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ScheduleId    int64                  `protobuf:"varint,1,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadScheduleByID) Reset() {
	*x = ThreadScheduleByID{}
	mi := &file_pb_thread_thread_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadScheduleByID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadScheduleByID) ProtoMessage() {}

func (x *ThreadScheduleByID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadScheduleByID.ProtoReflect.Descriptor instead.
func (*ThreadScheduleByID) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{25}
}

func (x *ThreadScheduleByID) GetScheduleId() int64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

type ThreadSchedulesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadSchedulesRequest) Reset() {
	*x = ThreadSchedulesRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadSchedulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadSchedulesRequest) ProtoMessage() {}

func (x *ThreadSchedulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadSchedulesRequest.ProtoReflect.Descriptor instead.
func (*ThreadSchedulesRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{26}
}

func (x *ThreadSchedulesRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

type ThreadSchedulesResponse struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Schedules     []*ThreadScheduleResponse `protobuf:"bytes,1,rep,name=schedules,proto3" json:"schedules,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadSchedulesResponse) Reset() {
	*x = ThreadSchedulesResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadSchedulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadSchedulesResponse) ProtoMessage() {}

func (x *ThreadSchedulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadSchedulesResponse.ProtoReflect.Descriptor instead.
func (*ThreadSchedulesResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{27}
}

func (x *ThreadSchedulesResponse) GetSchedules() []*ThreadScheduleResponse {
	if x != nil {
		return x.Schedules
	}
	return nil
}

// ========================================================
//
//	ATTENDANCE SESSIONS
//
// ========================================================
type AttendanceSession struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Date          string                 `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`                                         // YYYY-MM-DD format
	DayOfWeek     uint32                 `protobuf:"varint,2,opt,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week,omitempty"`           // 1=Mon … 7=Sun
	StartTime     string                 `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`              // "HH:MM:SS"
	EndTime       string                 `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                    // "HH:MM:SS"
	Location      string                 `protobuf:"bytes,5,opt,name=location,proto3" json:"location,omitempty"`                                 // Cabinet/room name
	HasAttendance bool                   `protobuf:"varint,6,opt,name=has_attendance,json=hasAttendance,proto3" json:"has_attendance,omitempty"` // Whether attendance has been marked for this session
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AttendanceSession) Reset() {
	*x = AttendanceSession{}
	mi := &file_pb_thread_thread_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttendanceSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttendanceSession) ProtoMessage() {}

func (x *AttendanceSession) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttendanceSession.ProtoReflect.Descriptor instead.
func (*AttendanceSession) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{28}
}

func (x *AttendanceSession) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *AttendanceSession) GetDayOfWeek() uint32 {
	if x != nil {
		return x.DayOfWeek
	}
	return 0
}

func (x *AttendanceSession) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *AttendanceSession) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *AttendanceSession) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *AttendanceSession) GetHasAttendance() bool {
	if x != nil {
		return x.HasAttendance
	}
	return false
}

type AttendanceSessionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sessions      []*AttendanceSession   `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AttendanceSessionsResponse) Reset() {
	*x = AttendanceSessionsResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttendanceSessionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttendanceSessionsResponse) ProtoMessage() {}

func (x *AttendanceSessionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttendanceSessionsResponse.ProtoReflect.Descriptor instead.
func (*AttendanceSessionsResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{29}
}

func (x *AttendanceSessionsResponse) GetSessions() []*AttendanceSession {
	if x != nil {
		return x.Sessions
	}
	return nil
}

// ========================================================
//
//	THREADS FOR USER (rich)
//
// ========================================================
type UserThreadsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserThreadsRequest) Reset() {
	*x = UserThreadsRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserThreadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserThreadsRequest) ProtoMessage() {}

func (x *UserThreadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserThreadsRequest.ProtoReflect.Descriptor instead.
func (*UserThreadsRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{30}
}

func (x *UserThreadsRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CourseInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title          string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description    string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	BannerImageUrl string                 `protobuf:"bytes,4,opt,name=banner_image_url,json=bannerImageUrl,proto3" json:"banner_image_url,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CourseInfo) Reset() {
	*x = CourseInfo{}
	mi := &file_pb_thread_thread_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CourseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CourseInfo) ProtoMessage() {}

func (x *CourseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CourseInfo.ProtoReflect.Descriptor instead.
func (*CourseInfo) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{31}
}

func (x *CourseInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CourseInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CourseInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CourseInfo) GetBannerImageUrl() string {
	if x != nil {
		return x.BannerImageUrl
	}
	return ""
}

type SemesterInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterInfo) Reset() {
	*x = SemesterInfo{}
	mi := &file_pb_thread_thread_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterInfo) ProtoMessage() {}

func (x *SemesterInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterInfo.ProtoReflect.Descriptor instead.
func (*SemesterInfo) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{32}
}

func (x *SemesterInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SemesterInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SemesterInfo) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *SemesterInfo) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type TeacherInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Surname       string                 `protobuf:"bytes,3,opt,name=surname,proto3" json:"surname,omitempty"`
	Email         string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TeacherInfo) Reset() {
	*x = TeacherInfo{}
	mi := &file_pb_thread_thread_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TeacherInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeacherInfo) ProtoMessage() {}

func (x *TeacherInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeacherInfo.ProtoReflect.Descriptor instead.
func (*TeacherInfo) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{33}
}

func (x *TeacherInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TeacherInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TeacherInfo) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *TeacherInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type ThreadWithDetails struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Thread        *ThreadResponse        `protobuf:"bytes,1,opt,name=thread,proto3" json:"thread,omitempty"`
	Course        *CourseInfo            `protobuf:"bytes,2,opt,name=course,proto3" json:"course,omitempty"`
	Semester      *SemesterInfo          `protobuf:"bytes,3,opt,name=semester,proto3" json:"semester,omitempty"`
	Teacher       *TeacherInfo           `protobuf:"bytes,4,opt,name=teacher,proto3" json:"teacher,omitempty"`
	FinalGrade    float64                `protobuf:"fixed64,5,opt,name=final_grade,json=finalGrade,proto3" json:"final_grade,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadWithDetails) Reset() {
	*x = ThreadWithDetails{}
	mi := &file_pb_thread_thread_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadWithDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadWithDetails) ProtoMessage() {}

func (x *ThreadWithDetails) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadWithDetails.ProtoReflect.Descriptor instead.
func (*ThreadWithDetails) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{34}
}

func (x *ThreadWithDetails) GetThread() *ThreadResponse {
	if x != nil {
		return x.Thread
	}
	return nil
}

func (x *ThreadWithDetails) GetCourse() *CourseInfo {
	if x != nil {
		return x.Course
	}
	return nil
}

func (x *ThreadWithDetails) GetSemester() *SemesterInfo {
	if x != nil {
		return x.Semester
	}
	return nil
}

func (x *ThreadWithDetails) GetTeacher() *TeacherInfo {
	if x != nil {
		return x.Teacher
	}
	return nil
}

func (x *ThreadWithDetails) GetFinalGrade() float64 {
	if x != nil {
		return x.FinalGrade
	}
	return 0
}

type UserThreadsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Threads       []*ThreadWithDetails   `protobuf:"bytes,1,rep,name=threads,proto3" json:"threads,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserThreadsResponse) Reset() {
	*x = UserThreadsResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserThreadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserThreadsResponse) ProtoMessage() {}

func (x *UserThreadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserThreadsResponse.ProtoReflect.Descriptor instead.
func (*UserThreadsResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{35}
}

func (x *UserThreadsResponse) GetThreads() []*ThreadWithDetails {
	if x != nil {
		return x.Threads
	}
	return nil
}

type WeeksWithHwRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"` // обязательный
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`       // для кого подтягиваем submission/score
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WeeksWithHwRequest) Reset() {
	*x = WeeksWithHwRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeeksWithHwRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeeksWithHwRequest) ProtoMessage() {}

func (x *WeeksWithHwRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeeksWithHwRequest.ProtoReflect.Descriptor instead.
func (*WeeksWithHwRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{36}
}

func (x *WeeksWithHwRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *WeeksWithHwRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type AssignmentWithSubmission struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	Assignment    *assignment.AssignmentResponse           `protobuf:"bytes,1,opt,name=assignment,proto3" json:"assignment,omitempty"`
	Submission    *assignment.AssignmentSubmissionResponse `protobuf:"bytes,2,opt,name=submission,proto3" json:"submission,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentWithSubmission) Reset() {
	*x = AssignmentWithSubmission{}
	mi := &file_pb_thread_thread_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentWithSubmission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentWithSubmission) ProtoMessage() {}

func (x *AssignmentWithSubmission) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentWithSubmission.ProtoReflect.Descriptor instead.
func (*AssignmentWithSubmission) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{37}
}

func (x *AssignmentWithSubmission) GetAssignment() *assignment.AssignmentResponse {
	if x != nil {
		return x.Assignment
	}
	return nil
}

func (x *AssignmentWithSubmission) GetSubmission() *assignment.AssignmentSubmissionResponse {
	if x != nil {
		return x.Submission
	}
	return nil
}

type WeekWithHw struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Week          *WeekResponse               `protobuf:"bytes,1,opt,name=week,proto3" json:"week,omitempty"` // ваш уже существующий message
	Assignments   []*AssignmentWithSubmission `protobuf:"bytes,2,rep,name=assignments,proto3" json:"assignments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WeekWithHw) Reset() {
	*x = WeekWithHw{}
	mi := &file_pb_thread_thread_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeekWithHw) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeekWithHw) ProtoMessage() {}

func (x *WeekWithHw) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeekWithHw.ProtoReflect.Descriptor instead.
func (*WeekWithHw) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{38}
}

func (x *WeekWithHw) GetWeek() *WeekResponse {
	if x != nil {
		return x.Week
	}
	return nil
}

func (x *WeekWithHw) GetAssignments() []*AssignmentWithSubmission {
	if x != nil {
		return x.Assignments
	}
	return nil
}

type WeeksWithHwResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Weeks         []*WeekWithHw          `protobuf:"bytes,1,rep,name=weeks,proto3" json:"weeks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WeeksWithHwResponse) Reset() {
	*x = WeeksWithHwResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeeksWithHwResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeeksWithHwResponse) ProtoMessage() {}

func (x *WeeksWithHwResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeeksWithHwResponse.ProtoReflect.Descriptor instead.
func (*WeeksWithHwResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{39}
}

func (x *WeeksWithHwResponse) GetWeeks() []*WeekWithHw {
	if x != nil {
		return x.Weeks
	}
	return nil
}

// ========================================================
//
//	LOCATION
//
// ========================================================
type LocationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Capacity      int32                  `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationRequest) Reset() {
	*x = LocationRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationRequest) ProtoMessage() {}

func (x *LocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationRequest.ProtoReflect.Descriptor instead.
func (*LocationRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{40}
}

func (x *LocationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LocationRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LocationRequest) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

type LocationUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Capacity      int32                  `protobuf:"varint,4,opt,name=capacity,proto3" json:"capacity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationUpdateRequest) Reset() {
	*x = LocationUpdateRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationUpdateRequest) ProtoMessage() {}

func (x *LocationUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationUpdateRequest.ProtoReflect.Descriptor instead.
func (*LocationUpdateRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{41}
}

func (x *LocationUpdateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LocationUpdateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LocationUpdateRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LocationUpdateRequest) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

type LocationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Capacity      int32                  `protobuf:"varint,4,opt,name=capacity,proto3" json:"capacity,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationResponse) Reset() {
	*x = LocationResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationResponse) ProtoMessage() {}

func (x *LocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationResponse.ProtoReflect.Descriptor instead.
func (*LocationResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{42}
}

func (x *LocationResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LocationResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LocationResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LocationResponse) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *LocationResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LocationResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type LocationByID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LocationId    int64                  `protobuf:"varint,1,opt,name=location_id,json=locationId,proto3" json:"location_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationByID) Reset() {
	*x = LocationByID{}
	mi := &file_pb_thread_thread_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationByID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationByID) ProtoMessage() {}

func (x *LocationByID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationByID.ProtoReflect.Descriptor instead.
func (*LocationByID) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{43}
}

func (x *LocationByID) GetLocationId() int64 {
	if x != nil {
		return x.LocationId
	}
	return 0
}

type LocationEmptyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationEmptyRequest) Reset() {
	*x = LocationEmptyRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationEmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationEmptyRequest) ProtoMessage() {}

func (x *LocationEmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationEmptyRequest.ProtoReflect.Descriptor instead.
func (*LocationEmptyRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{44}
}

type LocationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Locations     []*LocationResponse    `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationsResponse) Reset() {
	*x = LocationsResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationsResponse) ProtoMessage() {}

func (x *LocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationsResponse.ProtoReflect.Descriptor instead.
func (*LocationsResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{45}
}

func (x *LocationsResponse) GetLocations() []*LocationResponse {
	if x != nil {
		return x.Locations
	}
	return nil
}

type LocationAvailabilityRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	LocationId        int64                  `protobuf:"varint,1,opt,name=location_id,json=locationId,proto3" json:"location_id,omitempty"`
	DayOfWeek         uint32                 `protobuf:"varint,2,opt,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week,omitempty"`
	StartTime         string                 `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime           string                 `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ExcludeScheduleId int64                  `protobuf:"varint,5,opt,name=exclude_schedule_id,json=excludeScheduleId,proto3" json:"exclude_schedule_id,omitempty"` // Optional, to exclude a specific schedule when checking
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *LocationAvailabilityRequest) Reset() {
	*x = LocationAvailabilityRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationAvailabilityRequest) ProtoMessage() {}

func (x *LocationAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*LocationAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{46}
}

func (x *LocationAvailabilityRequest) GetLocationId() int64 {
	if x != nil {
		return x.LocationId
	}
	return 0
}

func (x *LocationAvailabilityRequest) GetDayOfWeek() uint32 {
	if x != nil {
		return x.DayOfWeek
	}
	return 0
}

func (x *LocationAvailabilityRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *LocationAvailabilityRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *LocationAvailabilityRequest) GetExcludeScheduleId() int64 {
	if x != nil {
		return x.ExcludeScheduleId
	}
	return 0
}

type LocationAvailabilityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Available     bool                   `protobuf:"varint,1,opt,name=available,proto3" json:"available,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationAvailabilityResponse) Reset() {
	*x = LocationAvailabilityResponse{}
	mi := &file_pb_thread_thread_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationAvailabilityResponse) ProtoMessage() {}

func (x *LocationAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*LocationAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{47}
}

func (x *LocationAvailabilityResponse) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

// ========================================================
//
//	PRE-REQUISITES
//
// ========================================================
type CheckPrerequisitesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckPrerequisitesRequest) Reset() {
	*x = CheckPrerequisitesRequest{}
	mi := &file_pb_thread_thread_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckPrerequisitesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPrerequisitesRequest) ProtoMessage() {}

func (x *CheckPrerequisitesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_thread_thread_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPrerequisitesRequest.ProtoReflect.Descriptor instead.
func (*CheckPrerequisitesRequest) Descriptor() ([]byte, []int) {
	return file_pb_thread_thread_proto_rawDescGZIP(), []int{48}
}

func (x *CheckPrerequisitesRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CheckPrerequisitesRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

var File_pb_thread_thread_proto protoreflect.FileDescriptor

const file_pb_thread_thread_proto_rawDesc = "" +
	"\n" +
	"\x16pb/thread/thread.proto\x12\bthreadpb\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1epb/assignment/assignment.proto\"\xc8\x01\n" +
	"\rThreadRequest\x12\x1b\n" +
	"\tcourse_id\x18\x01 \x01(\x03R\bcourseId\x12\x1f\n" +
	"\vsemester_id\x18\x02 \x01(\x03R\n" +
	"semesterId\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x03 \x01(\x03R\tteacherId\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12!\n" +
	"\fsyllabus_url\x18\x05 \x01(\tR\vsyllabusUrl\x12!\n" +
	"\fmax_students\x18\x06 \x01(\x05R\vmaxStudents\"\xde\x01\n" +
	"\x13ThreadUpdateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tcourse_id\x18\x02 \x01(\x03R\bcourseId\x12\x1f\n" +
	"\vsemester_id\x18\x03 \x01(\x03R\n" +
	"semesterId\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x04 \x01(\x03R\tteacherId\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12!\n" +
	"\fsyllabus_url\x18\x06 \x01(\tR\vsyllabusUrl\x12!\n" +
	"\fmax_students\x18\a \x01(\x05R\vmaxStudents\"\xcf\x02\n" +
	"\x0eThreadResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tcourse_id\x18\x02 \x01(\x03R\bcourseId\x12\x1f\n" +
	"\vsemester_id\x18\x03 \x01(\x03R\n" +
	"semesterId\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x04 \x01(\x03R\tteacherId\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12!\n" +
	"\fsyllabus_url\x18\x06 \x01(\tR\vsyllabusUrl\x12!\n" +
	"\fmax_students\x18\a \x01(\x05R\vmaxStudents\x129\n" +
	"\n" +
	"created_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\")\n" +
	"\n" +
	"ThreadByID\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\"5\n" +
	"\x16ThreadsByCourseRequest\x12\x1b\n" +
	"\tcourse_id\x18\x01 \x01(\x03R\bcourseId\"E\n" +
	"\x0fThreadsResponse\x122\n" +
	"\athreads\x18\x01 \x03(\v2\x18.threadpb.ThreadResponseR\athreads\"\x14\n" +
	"\x12ThreadEmptyRequest\"\x15\n" +
	"\x13ThreadEmptyResponse\"S\n" +
	"\x1bRegisterUserToThreadRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\"X\n" +
	" RegisterManyUsersToThreadRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12\x17\n" +
	"\auser_id\x18\x02 \x03(\x03R\x06userId\"Y\n" +
	"!RemoveRegistrationToThreadRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\"^\n" +
	"&RemoveManyRegistrationsToThreadRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12\x17\n" +
	"\auser_id\x18\x02 \x03(\x03R\x06userId\"s\n" +
	"\x1aThreadRegistrationResponse\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\x12\x1f\n" +
	"\vfinal_grade\x18\x03 \x01(\x02R\n" +
	"finalGrade\"z\n" +
	"\x1fListThreadRegistrationsResponse\x12W\n" +
	"\x14thread_registrations\x18\x01 \x03(\v2$.threadpb.ThreadRegistrationResponseR\x13threadRegistrations\"|\n" +
	"#GradeFinalThreadRegistrationRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\x12\x1f\n" +
	"\vfinal_grade\x18\x03 \x01(\x01R\n" +
	"finalGrade\"\x8a\x01\n" +
	"$GradeFinalThreadRegistrationResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\x12H\n" +
	"\fregistration\x18\x02 \x01(\v2$.threadpb.ThreadRegistrationResponseR\fregistration\"\x97\x01\n" +
	"\vWeekRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12\x1f\n" +
	"\vweek_number\x18\x02 \x01(\rR\n" +
	"weekNumber\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\"\xb6\x01\n" +
	"\x11WeekUpdateRequest\x12\x17\n" +
	"\aweek_id\x18\x01 \x01(\x03R\x06weekId\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\x12\x1f\n" +
	"\vweek_number\x18\x03 \x01(\rR\n" +
	"weekNumber\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\"\x9e\x02\n" +
	"\fWeekResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\x12\x1f\n" +
	"\vweek_number\x18\x03 \x01(\rR\n" +
	"weekNumber\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"#\n" +
	"\bWeekByID\x12\x17\n" +
	"\aweek_id\x18\x01 \x01(\x03R\x06weekId\"4\n" +
	"\x15WeeksForThreadRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\"=\n" +
	"\rWeeksResponse\x12,\n" +
	"\x05weeks\x18\x01 \x03(\v2\x16.threadpb.WeekResponseR\x05weeks\"\xaa\x01\n" +
	"\x15ThreadScheduleRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12\x1e\n" +
	"\vday_of_week\x18\x02 \x01(\rR\tdayOfWeek\x12\x1d\n" +
	"\n" +
	"start_time\x18\x03 \x01(\tR\tstartTime\x12\x19\n" +
	"\bend_time\x18\x04 \x01(\tR\aendTime\x12\x1a\n" +
	"\blocation\x18\x05 \x01(\tR\blocation\"\xc0\x01\n" +
	"\x1bThreadScheduleUpdateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\x12\x1e\n" +
	"\vday_of_week\x18\x03 \x01(\rR\tdayOfWeek\x12\x1d\n" +
	"\n" +
	"start_time\x18\x04 \x01(\tR\tstartTime\x12\x19\n" +
	"\bend_time\x18\x05 \x01(\tR\aendTime\x12\x1a\n" +
	"\blocation\x18\x06 \x01(\tR\blocation\"\xb1\x02\n" +
	"\x16ThreadScheduleResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\x12\x1e\n" +
	"\vday_of_week\x18\x03 \x01(\rR\tdayOfWeek\x12\x1d\n" +
	"\n" +
	"start_time\x18\x04 \x01(\tR\tstartTime\x12\x19\n" +
	"\bend_time\x18\x05 \x01(\tR\aendTime\x12\x1a\n" +
	"\blocation\x18\x06 \x01(\tR\blocation\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"5\n" +
	"\x12ThreadScheduleByID\x12\x1f\n" +
	"\vschedule_id\x18\x01 \x01(\x03R\n" +
	"scheduleId\"5\n" +
	"\x16ThreadSchedulesRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\"Y\n" +
	"\x17ThreadSchedulesResponse\x12>\n" +
	"\tschedules\x18\x01 \x03(\v2 .threadpb.ThreadScheduleResponseR\tschedules\"\xc4\x01\n" +
	"\x11AttendanceSession\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x12\x1e\n" +
	"\vday_of_week\x18\x02 \x01(\rR\tdayOfWeek\x12\x1d\n" +
	"\n" +
	"start_time\x18\x03 \x01(\tR\tstartTime\x12\x19\n" +
	"\bend_time\x18\x04 \x01(\tR\aendTime\x12\x1a\n" +
	"\blocation\x18\x05 \x01(\tR\blocation\x12%\n" +
	"\x0ehas_attendance\x18\x06 \x01(\bR\rhasAttendance\"U\n" +
	"\x1aAttendanceSessionsResponse\x127\n" +
	"\bsessions\x18\x01 \x03(\v2\x1b.threadpb.AttendanceSessionR\bsessions\"-\n" +
	"\x12UserThreadsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\"~\n" +
	"\n" +
	"CourseInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12(\n" +
	"\x10banner_image_url\x18\x04 \x01(\tR\x0ebannerImageUrl\"\xa4\x01\n" +
	"\fSemesterInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"a\n" +
	"\vTeacherInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\asurname\x18\x03 \x01(\tR\asurname\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\"\xf9\x01\n" +
	"\x11ThreadWithDetails\x120\n" +
	"\x06thread\x18\x01 \x01(\v2\x18.threadpb.ThreadResponseR\x06thread\x12,\n" +
	"\x06course\x18\x02 \x01(\v2\x14.threadpb.CourseInfoR\x06course\x122\n" +
	"\bsemester\x18\x03 \x01(\v2\x16.threadpb.SemesterInfoR\bsemester\x12/\n" +
	"\ateacher\x18\x04 \x01(\v2\x15.threadpb.TeacherInfoR\ateacher\x12\x1f\n" +
	"\vfinal_grade\x18\x05 \x01(\x01R\n" +
	"finalGrade\"L\n" +
	"\x13UserThreadsResponse\x125\n" +
	"\athreads\x18\x01 \x03(\v2\x1b.threadpb.ThreadWithDetailsR\athreads\"J\n" +
	"\x12WeeksWithHwRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\"\xa8\x01\n" +
	"\x18AssignmentWithSubmission\x12@\n" +
	"\n" +
	"assignment\x18\x01 \x01(\v2 .assignmentpb.AssignmentResponseR\n" +
	"assignment\x12J\n" +
	"\n" +
	"submission\x18\x02 \x01(\v2*.assignmentpb.AssignmentSubmissionResponseR\n" +
	"submission\"~\n" +
	"\n" +
	"WeekWithHw\x12*\n" +
	"\x04week\x18\x01 \x01(\v2\x16.threadpb.WeekResponseR\x04week\x12D\n" +
	"\vassignments\x18\x02 \x03(\v2\".threadpb.AssignmentWithSubmissionR\vassignments\"A\n" +
	"\x13WeeksWithHwResponse\x12*\n" +
	"\x05weeks\x18\x01 \x03(\v2\x14.threadpb.WeekWithHwR\x05weeks\"c\n" +
	"\x0fLocationRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x1a\n" +
	"\bcapacity\x18\x03 \x01(\x05R\bcapacity\"y\n" +
	"\x15LocationUpdateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1a\n" +
	"\bcapacity\x18\x04 \x01(\x05R\bcapacity\"\xea\x01\n" +
	"\x10LocationResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1a\n" +
	"\bcapacity\x18\x04 \x01(\x05R\bcapacity\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"/\n" +
	"\fLocationByID\x12\x1f\n" +
	"\vlocation_id\x18\x01 \x01(\x03R\n" +
	"locationId\"\x16\n" +
	"\x14LocationEmptyRequest\"M\n" +
	"\x11LocationsResponse\x128\n" +
	"\tlocations\x18\x01 \x03(\v2\x1a.threadpb.LocationResponseR\tlocations\"\xc8\x01\n" +
	"\x1bLocationAvailabilityRequest\x12\x1f\n" +
	"\vlocation_id\x18\x01 \x01(\x03R\n" +
	"locationId\x12\x1e\n" +
	"\vday_of_week\x18\x02 \x01(\rR\tdayOfWeek\x12\x1d\n" +
	"\n" +
	"start_time\x18\x03 \x01(\tR\tstartTime\x12\x19\n" +
	"\bend_time\x18\x04 \x01(\tR\aendTime\x12.\n" +
	"\x13exclude_schedule_id\x18\x05 \x01(\x03R\x11excludeScheduleId\"<\n" +
	"\x1cLocationAvailabilityResponse\x12\x1c\n" +
	"\tavailable\x18\x01 \x01(\bR\tavailable\"Q\n" +
	"\x19CheckPrerequisitesRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId2\xd0\x15\n" +
	"\rThreadService\x12C\n" +
	"\fCreateThread\x12\x17.threadpb.ThreadRequest\x1a\x18.threadpb.ThreadResponse\"\x00\x12T\n" +
	"\x13ListThreadsByCourse\x12 .threadpb.ThreadsByCourseRequest\x1a\x19.threadpb.ThreadsResponse\"\x00\x12H\n" +
	"\vListThreads\x12\x1c.threadpb.ThreadEmptyRequest\x1a\x19.threadpb.ThreadsResponse\"\x00\x12A\n" +
	"\rGetThreadByID\x12\x14.threadpb.ThreadByID\x1a\x18.threadpb.ThreadResponse\"\x00\x12M\n" +
	"\x10UpdateThreadByID\x12\x1d.threadpb.ThreadUpdateRequest\x1a\x18.threadpb.ThreadResponse\"\x00\x12I\n" +
	"\x10DeleteThreadByID\x12\x14.threadpb.ThreadByID\x1a\x1d.threadpb.ThreadEmptyResponse\"\x00\x12^\n" +
	"\x14RegisterUserToThread\x12%.threadpb.RegisterUserToThreadRequest\x1a\x1d.threadpb.ThreadEmptyResponse\"\x00\x12h\n" +
	"\x19RegisterManyUsersToThread\x12*.threadpb.RegisterManyUsersToThreadRequest\x1a\x1d.threadpb.ThreadEmptyResponse\"\x00\x12j\n" +
	"\x1aRemoveRegistrationToThread\x12+.threadpb.RemoveRegistrationToThreadRequest\x1a\x1d.threadpb.ThreadEmptyResponse\"\x00\x12t\n" +
	"\x1fRemoveManyRegistrationsToThread\x120.threadpb.RemoveManyRegistrationsToThreadRequest\x1a\x1d.threadpb.ThreadEmptyResponse\"\x00\x12\\\n" +
	"\x17ListThreadRegistrations\x12\x14.threadpb.ThreadByID\x1a).threadpb.ListThreadRegistrationsResponse\"\x00\x12\x7f\n" +
	"\x1cGradeFinalThreadRegistration\x12-.threadpb.GradeFinalThreadRegistrationRequest\x1a..threadpb.GradeFinalThreadRegistrationResponse\"\x00\x12=\n" +
	"\n" +
	"CreateWeek\x12\x15.threadpb.WeekRequest\x1a\x16.threadpb.WeekResponse\"\x00\x12P\n" +
	"\x12ListWeeksForThread\x12\x1f.threadpb.WeeksForThreadRequest\x1a\x17.threadpb.WeeksResponse\"\x00\x12;\n" +
	"\vGetWeekByID\x12\x12.threadpb.WeekByID\x1a\x16.threadpb.WeekResponse\"\x00\x12C\n" +
	"\n" +
	"UpdateWeek\x12\x1b.threadpb.WeekUpdateRequest\x1a\x16.threadpb.WeekResponse\"\x00\x12E\n" +
	"\x0eDeleteWeekByID\x12\x12.threadpb.WeekByID\x1a\x1d.threadpb.ThreadEmptyResponse\"\x00\x12[\n" +
	"\x14CreateThreadSchedule\x12\x1f.threadpb.ThreadScheduleRequest\x1a .threadpb.ThreadScheduleResponse\"\x00\x12\\\n" +
	"\x13ListThreadSchedules\x12 .threadpb.ThreadSchedulesRequest\x1a!.threadpb.ThreadSchedulesResponse\"\x00\x12Y\n" +
	"\x15GetThreadScheduleByID\x12\x1c.threadpb.ThreadScheduleByID\x1a .threadpb.ThreadScheduleResponse\"\x00\x12a\n" +
	"\x14UpdateThreadSchedule\x12%.threadpb.ThreadScheduleUpdateRequest\x1a .threadpb.ThreadScheduleResponse\"\x00\x12Y\n" +
	"\x18DeleteThreadScheduleByID\x12\x1c.threadpb.ThreadScheduleByID\x1a\x1d.threadpb.ThreadEmptyResponse\"\x00\x12V\n" +
	"\x16ListAttendanceSessions\x12\x14.threadpb.ThreadByID\x1a$.threadpb.AttendanceSessionsResponse\"\x00\x12I\n" +
	"\x0eCreateLocation\x12\x19.threadpb.LocationRequest\x1a\x1a.threadpb.LocationResponse\"\x00\x12G\n" +
	"\x0fGetLocationByID\x12\x16.threadpb.LocationByID\x1a\x1a.threadpb.LocationResponse\"\x00\x12N\n" +
	"\rListLocations\x12\x1e.threadpb.LocationEmptyRequest\x1a\x1b.threadpb.LocationsResponse\"\x00\x12O\n" +
	"\x0eUpdateLocation\x12\x1f.threadpb.LocationUpdateRequest\x1a\x1a.threadpb.LocationResponse\"\x00\x12I\n" +
	"\x0eDeleteLocation\x12\x16.threadpb.LocationByID\x1a\x1d.threadpb.ThreadEmptyResponse\"\x00\x12l\n" +
	"\x19CheckLocationAvailability\x12%.threadpb.LocationAvailabilityRequest\x1a&.threadpb.LocationAvailabilityResponse\"\x00\x12S\n" +
	"\x12ListThreadsForUser\x12\x1c.threadpb.UserThreadsRequest\x1a\x1d.threadpb.UserThreadsResponse\"\x00\x12V\n" +
	"\x15ListWeeksWithHomework\x12\x1c.threadpb.WeeksWithHwRequest\x1a\x1d.threadpb.WeeksWithHwResponse\"\x00\x12Q\n" +
	"\x12CheckPrerequisites\x12#.threadpb.CheckPrerequisitesRequest\x1a\x16.google.protobuf.EmptyBEZCgithub.com/olzzhas/edunite-server/course_service/pb/thread;threadpbb\x06proto3"

var (
	file_pb_thread_thread_proto_rawDescOnce sync.Once
	file_pb_thread_thread_proto_rawDescData []byte
)

func file_pb_thread_thread_proto_rawDescGZIP() []byte {
	file_pb_thread_thread_proto_rawDescOnce.Do(func() {
		file_pb_thread_thread_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_thread_thread_proto_rawDesc), len(file_pb_thread_thread_proto_rawDesc)))
	})
	return file_pb_thread_thread_proto_rawDescData
}

var file_pb_thread_thread_proto_msgTypes = make([]protoimpl.MessageInfo, 49)
var file_pb_thread_thread_proto_goTypes = []any{
	(*ThreadRequest)(nil),                           // 0: threadpb.ThreadRequest
	(*ThreadUpdateRequest)(nil),                     // 1: threadpb.ThreadUpdateRequest
	(*ThreadResponse)(nil),                          // 2: threadpb.ThreadResponse
	(*ThreadByID)(nil),                              // 3: threadpb.ThreadByID
	(*ThreadsByCourseRequest)(nil),                  // 4: threadpb.ThreadsByCourseRequest
	(*ThreadsResponse)(nil),                         // 5: threadpb.ThreadsResponse
	(*ThreadEmptyRequest)(nil),                      // 6: threadpb.ThreadEmptyRequest
	(*ThreadEmptyResponse)(nil),                     // 7: threadpb.ThreadEmptyResponse
	(*RegisterUserToThreadRequest)(nil),             // 8: threadpb.RegisterUserToThreadRequest
	(*RegisterManyUsersToThreadRequest)(nil),        // 9: threadpb.RegisterManyUsersToThreadRequest
	(*RemoveRegistrationToThreadRequest)(nil),       // 10: threadpb.RemoveRegistrationToThreadRequest
	(*RemoveManyRegistrationsToThreadRequest)(nil),  // 11: threadpb.RemoveManyRegistrationsToThreadRequest
	(*ThreadRegistrationResponse)(nil),              // 12: threadpb.ThreadRegistrationResponse
	(*ListThreadRegistrationsResponse)(nil),         // 13: threadpb.ListThreadRegistrationsResponse
	(*GradeFinalThreadRegistrationRequest)(nil),     // 14: threadpb.GradeFinalThreadRegistrationRequest
	(*GradeFinalThreadRegistrationResponse)(nil),    // 15: threadpb.GradeFinalThreadRegistrationResponse
	(*WeekRequest)(nil),                             // 16: threadpb.WeekRequest
	(*WeekUpdateRequest)(nil),                       // 17: threadpb.WeekUpdateRequest
	(*WeekResponse)(nil),                            // 18: threadpb.WeekResponse
	(*WeekByID)(nil),                                // 19: threadpb.WeekByID
	(*WeeksForThreadRequest)(nil),                   // 20: threadpb.WeeksForThreadRequest
	(*WeeksResponse)(nil),                           // 21: threadpb.WeeksResponse
	(*ThreadScheduleRequest)(nil),                   // 22: threadpb.ThreadScheduleRequest
	(*ThreadScheduleUpdateRequest)(nil),             // 23: threadpb.ThreadScheduleUpdateRequest
	(*ThreadScheduleResponse)(nil),                  // 24: threadpb.ThreadScheduleResponse
	(*ThreadScheduleByID)(nil),                      // 25: threadpb.ThreadScheduleByID
	(*ThreadSchedulesRequest)(nil),                  // 26: threadpb.ThreadSchedulesRequest
	(*ThreadSchedulesResponse)(nil),                 // 27: threadpb.ThreadSchedulesResponse
	(*AttendanceSession)(nil),                       // 28: threadpb.AttendanceSession
	(*AttendanceSessionsResponse)(nil),              // 29: threadpb.AttendanceSessionsResponse
	(*UserThreadsRequest)(nil),                      // 30: threadpb.UserThreadsRequest
	(*CourseInfo)(nil),                              // 31: threadpb.CourseInfo
	(*SemesterInfo)(nil),                            // 32: threadpb.SemesterInfo
	(*TeacherInfo)(nil),                             // 33: threadpb.TeacherInfo
	(*ThreadWithDetails)(nil),                       // 34: threadpb.ThreadWithDetails
	(*UserThreadsResponse)(nil),                     // 35: threadpb.UserThreadsResponse
	(*WeeksWithHwRequest)(nil),                      // 36: threadpb.WeeksWithHwRequest
	(*AssignmentWithSubmission)(nil),                // 37: threadpb.AssignmentWithSubmission
	(*WeekWithHw)(nil),                              // 38: threadpb.WeekWithHw
	(*WeeksWithHwResponse)(nil),                     // 39: threadpb.WeeksWithHwResponse
	(*LocationRequest)(nil),                         // 40: threadpb.LocationRequest
	(*LocationUpdateRequest)(nil),                   // 41: threadpb.LocationUpdateRequest
	(*LocationResponse)(nil),                        // 42: threadpb.LocationResponse
	(*LocationByID)(nil),                            // 43: threadpb.LocationByID
	(*LocationEmptyRequest)(nil),                    // 44: threadpb.LocationEmptyRequest
	(*LocationsResponse)(nil),                       // 45: threadpb.LocationsResponse
	(*LocationAvailabilityRequest)(nil),             // 46: threadpb.LocationAvailabilityRequest
	(*LocationAvailabilityResponse)(nil),            // 47: threadpb.LocationAvailabilityResponse
	(*CheckPrerequisitesRequest)(nil),               // 48: threadpb.CheckPrerequisitesRequest
	(*timestamppb.Timestamp)(nil),                   // 49: google.protobuf.Timestamp
	(*assignment.AssignmentResponse)(nil),           // 50: assignmentpb.AssignmentResponse
	(*assignment.AssignmentSubmissionResponse)(nil), // 51: assignmentpb.AssignmentSubmissionResponse
	(*emptypb.Empty)(nil),                           // 52: google.protobuf.Empty
}
var file_pb_thread_thread_proto_depIdxs = []int32{
	49, // 0: threadpb.ThreadResponse.created_at:type_name -> google.protobuf.Timestamp
	49, // 1: threadpb.ThreadResponse.updated_at:type_name -> google.protobuf.Timestamp
	2,  // 2: threadpb.ThreadsResponse.threads:type_name -> threadpb.ThreadResponse
	12, // 3: threadpb.ListThreadRegistrationsResponse.thread_registrations:type_name -> threadpb.ThreadRegistrationResponse
	12, // 4: threadpb.GradeFinalThreadRegistrationResponse.registration:type_name -> threadpb.ThreadRegistrationResponse
	49, // 5: threadpb.WeekResponse.created_at:type_name -> google.protobuf.Timestamp
	49, // 6: threadpb.WeekResponse.updated_at:type_name -> google.protobuf.Timestamp
	18, // 7: threadpb.WeeksResponse.weeks:type_name -> threadpb.WeekResponse
	49, // 8: threadpb.ThreadScheduleResponse.created_at:type_name -> google.protobuf.Timestamp
	49, // 9: threadpb.ThreadScheduleResponse.updated_at:type_name -> google.protobuf.Timestamp
	24, // 10: threadpb.ThreadSchedulesResponse.schedules:type_name -> threadpb.ThreadScheduleResponse
	28, // 11: threadpb.AttendanceSessionsResponse.sessions:type_name -> threadpb.AttendanceSession
	49, // 12: threadpb.SemesterInfo.start_date:type_name -> google.protobuf.Timestamp
	49, // 13: threadpb.SemesterInfo.end_date:type_name -> google.protobuf.Timestamp
	2,  // 14: threadpb.ThreadWithDetails.thread:type_name -> threadpb.ThreadResponse
	31, // 15: threadpb.ThreadWithDetails.course:type_name -> threadpb.CourseInfo
	32, // 16: threadpb.ThreadWithDetails.semester:type_name -> threadpb.SemesterInfo
	33, // 17: threadpb.ThreadWithDetails.teacher:type_name -> threadpb.TeacherInfo
	34, // 18: threadpb.UserThreadsResponse.threads:type_name -> threadpb.ThreadWithDetails
	50, // 19: threadpb.AssignmentWithSubmission.assignment:type_name -> assignmentpb.AssignmentResponse
	51, // 20: threadpb.AssignmentWithSubmission.submission:type_name -> assignmentpb.AssignmentSubmissionResponse
	18, // 21: threadpb.WeekWithHw.week:type_name -> threadpb.WeekResponse
	37, // 22: threadpb.WeekWithHw.assignments:type_name -> threadpb.AssignmentWithSubmission
	38, // 23: threadpb.WeeksWithHwResponse.weeks:type_name -> threadpb.WeekWithHw
	49, // 24: threadpb.LocationResponse.created_at:type_name -> google.protobuf.Timestamp
	49, // 25: threadpb.LocationResponse.updated_at:type_name -> google.protobuf.Timestamp
	42, // 26: threadpb.LocationsResponse.locations:type_name -> threadpb.LocationResponse
	0,  // 27: threadpb.ThreadService.CreateThread:input_type -> threadpb.ThreadRequest
	4,  // 28: threadpb.ThreadService.ListThreadsByCourse:input_type -> threadpb.ThreadsByCourseRequest
	6,  // 29: threadpb.ThreadService.ListThreads:input_type -> threadpb.ThreadEmptyRequest
	3,  // 30: threadpb.ThreadService.GetThreadByID:input_type -> threadpb.ThreadByID
	1,  // 31: threadpb.ThreadService.UpdateThreadByID:input_type -> threadpb.ThreadUpdateRequest
	3,  // 32: threadpb.ThreadService.DeleteThreadByID:input_type -> threadpb.ThreadByID
	8,  // 33: threadpb.ThreadService.RegisterUserToThread:input_type -> threadpb.RegisterUserToThreadRequest
	9,  // 34: threadpb.ThreadService.RegisterManyUsersToThread:input_type -> threadpb.RegisterManyUsersToThreadRequest
	10, // 35: threadpb.ThreadService.RemoveRegistrationToThread:input_type -> threadpb.RemoveRegistrationToThreadRequest
	11, // 36: threadpb.ThreadService.RemoveManyRegistrationsToThread:input_type -> threadpb.RemoveManyRegistrationsToThreadRequest
	3,  // 37: threadpb.ThreadService.ListThreadRegistrations:input_type -> threadpb.ThreadByID
	14, // 38: threadpb.ThreadService.GradeFinalThreadRegistration:input_type -> threadpb.GradeFinalThreadRegistrationRequest
	16, // 39: threadpb.ThreadService.CreateWeek:input_type -> threadpb.WeekRequest
	20, // 40: threadpb.ThreadService.ListWeeksForThread:input_type -> threadpb.WeeksForThreadRequest
	19, // 41: threadpb.ThreadService.GetWeekByID:input_type -> threadpb.WeekByID
	17, // 42: threadpb.ThreadService.UpdateWeek:input_type -> threadpb.WeekUpdateRequest
	19, // 43: threadpb.ThreadService.DeleteWeekByID:input_type -> threadpb.WeekByID
	22, // 44: threadpb.ThreadService.CreateThreadSchedule:input_type -> threadpb.ThreadScheduleRequest
	26, // 45: threadpb.ThreadService.ListThreadSchedules:input_type -> threadpb.ThreadSchedulesRequest
	25, // 46: threadpb.ThreadService.GetThreadScheduleByID:input_type -> threadpb.ThreadScheduleByID
	23, // 47: threadpb.ThreadService.UpdateThreadSchedule:input_type -> threadpb.ThreadScheduleUpdateRequest
	25, // 48: threadpb.ThreadService.DeleteThreadScheduleByID:input_type -> threadpb.ThreadScheduleByID
	3,  // 49: threadpb.ThreadService.ListAttendanceSessions:input_type -> threadpb.ThreadByID
	40, // 50: threadpb.ThreadService.CreateLocation:input_type -> threadpb.LocationRequest
	43, // 51: threadpb.ThreadService.GetLocationByID:input_type -> threadpb.LocationByID
	44, // 52: threadpb.ThreadService.ListLocations:input_type -> threadpb.LocationEmptyRequest
	41, // 53: threadpb.ThreadService.UpdateLocation:input_type -> threadpb.LocationUpdateRequest
	43, // 54: threadpb.ThreadService.DeleteLocation:input_type -> threadpb.LocationByID
	46, // 55: threadpb.ThreadService.CheckLocationAvailability:input_type -> threadpb.LocationAvailabilityRequest
	30, // 56: threadpb.ThreadService.ListThreadsForUser:input_type -> threadpb.UserThreadsRequest
	36, // 57: threadpb.ThreadService.ListWeeksWithHomework:input_type -> threadpb.WeeksWithHwRequest
	48, // 58: threadpb.ThreadService.CheckPrerequisites:input_type -> threadpb.CheckPrerequisitesRequest
	2,  // 59: threadpb.ThreadService.CreateThread:output_type -> threadpb.ThreadResponse
	5,  // 60: threadpb.ThreadService.ListThreadsByCourse:output_type -> threadpb.ThreadsResponse
	5,  // 61: threadpb.ThreadService.ListThreads:output_type -> threadpb.ThreadsResponse
	2,  // 62: threadpb.ThreadService.GetThreadByID:output_type -> threadpb.ThreadResponse
	2,  // 63: threadpb.ThreadService.UpdateThreadByID:output_type -> threadpb.ThreadResponse
	7,  // 64: threadpb.ThreadService.DeleteThreadByID:output_type -> threadpb.ThreadEmptyResponse
	7,  // 65: threadpb.ThreadService.RegisterUserToThread:output_type -> threadpb.ThreadEmptyResponse
	7,  // 66: threadpb.ThreadService.RegisterManyUsersToThread:output_type -> threadpb.ThreadEmptyResponse
	7,  // 67: threadpb.ThreadService.RemoveRegistrationToThread:output_type -> threadpb.ThreadEmptyResponse
	7,  // 68: threadpb.ThreadService.RemoveManyRegistrationsToThread:output_type -> threadpb.ThreadEmptyResponse
	13, // 69: threadpb.ThreadService.ListThreadRegistrations:output_type -> threadpb.ListThreadRegistrationsResponse
	15, // 70: threadpb.ThreadService.GradeFinalThreadRegistration:output_type -> threadpb.GradeFinalThreadRegistrationResponse
	18, // 71: threadpb.ThreadService.CreateWeek:output_type -> threadpb.WeekResponse
	21, // 72: threadpb.ThreadService.ListWeeksForThread:output_type -> threadpb.WeeksResponse
	18, // 73: threadpb.ThreadService.GetWeekByID:output_type -> threadpb.WeekResponse
	18, // 74: threadpb.ThreadService.UpdateWeek:output_type -> threadpb.WeekResponse
	7,  // 75: threadpb.ThreadService.DeleteWeekByID:output_type -> threadpb.ThreadEmptyResponse
	24, // 76: threadpb.ThreadService.CreateThreadSchedule:output_type -> threadpb.ThreadScheduleResponse
	27, // 77: threadpb.ThreadService.ListThreadSchedules:output_type -> threadpb.ThreadSchedulesResponse
	24, // 78: threadpb.ThreadService.GetThreadScheduleByID:output_type -> threadpb.ThreadScheduleResponse
	24, // 79: threadpb.ThreadService.UpdateThreadSchedule:output_type -> threadpb.ThreadScheduleResponse
	7,  // 80: threadpb.ThreadService.DeleteThreadScheduleByID:output_type -> threadpb.ThreadEmptyResponse
	29, // 81: threadpb.ThreadService.ListAttendanceSessions:output_type -> threadpb.AttendanceSessionsResponse
	42, // 82: threadpb.ThreadService.CreateLocation:output_type -> threadpb.LocationResponse
	42, // 83: threadpb.ThreadService.GetLocationByID:output_type -> threadpb.LocationResponse
	45, // 84: threadpb.ThreadService.ListLocations:output_type -> threadpb.LocationsResponse
	42, // 85: threadpb.ThreadService.UpdateLocation:output_type -> threadpb.LocationResponse
	7,  // 86: threadpb.ThreadService.DeleteLocation:output_type -> threadpb.ThreadEmptyResponse
	47, // 87: threadpb.ThreadService.CheckLocationAvailability:output_type -> threadpb.LocationAvailabilityResponse
	35, // 88: threadpb.ThreadService.ListThreadsForUser:output_type -> threadpb.UserThreadsResponse
	39, // 89: threadpb.ThreadService.ListWeeksWithHomework:output_type -> threadpb.WeeksWithHwResponse
	52, // 90: threadpb.ThreadService.CheckPrerequisites:output_type -> google.protobuf.Empty
	59, // [59:91] is the sub-list for method output_type
	27, // [27:59] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_pb_thread_thread_proto_init() }
func file_pb_thread_thread_proto_init() {
	if File_pb_thread_thread_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_thread_thread_proto_rawDesc), len(file_pb_thread_thread_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   49,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_thread_thread_proto_goTypes,
		DependencyIndexes: file_pb_thread_thread_proto_depIdxs,
		MessageInfos:      file_pb_thread_thread_proto_msgTypes,
	}.Build()
	File_pb_thread_thread_proto = out.File
	file_pb_thread_thread_proto_goTypes = nil
	file_pb_thread_thread_proto_depIdxs = nil
}
