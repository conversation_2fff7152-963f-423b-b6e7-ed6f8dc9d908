package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupTranscriptRoutes sets up all transcript-related routes
func SetupTranscriptRoutes(router *gin.Engine, authClient *clients.AuthClient, transcriptHandler *handlers.TranscriptHandler) {
	// Public routes (no authentication required for some degree endpoints)
	public := router.Group("/")
	{
		// Degree management routes
		public.GET("/degrees", transcriptHandler.ListDegrees)
		public.GET("/degrees/:id", transcriptHandler.GetDegree)
	}

	// Protected routes (require authentication)
	protected := router.Group("/")
	protected.Use(AuthMiddleware(authClient)) // Apply authentication middleware
	{
		// Degree management routes (admin only)
		adminDegrees := protected.Group("/degrees")
		adminDegrees.Use(RoleMiddleware(authClient, "admin")) // Only admins can create/modify degrees
		{
			adminDegrees.POST("", transcriptHandler.CreateDegree)
		}

		// Transcript management routes
		transcripts := protected.Group("/transcripts")
		{
			// Create transcript (admin/teacher only)
			transcripts.POST("", RoleMiddleware(authClient, "admin", "teacher"), transcriptHandler.CreateTranscript)

			// Get transcript by user ID
			transcripts.GET("/user/:user_id", transcriptHandler.GetTranscript)

			// Add transcript entry (admin/teacher only)
			transcripts.POST("/entries", RoleMiddleware(authClient, "admin", "teacher"), transcriptHandler.AddTranscriptEntry)

			// Get transcript entries
			transcripts.GET("/:transcript_id/entries", transcriptHandler.GetTranscriptEntries)

			// Update GPA (admin/teacher only)
			transcripts.PUT("/:transcript_id/gpa", RoleMiddleware(authClient, "admin", "teacher"), transcriptHandler.UpdateGPA)

			// Generate transcript report
			transcripts.GET("/user/:user_id/report", transcriptHandler.GenerateTranscriptReport)
		}

		// Student degree management routes
		studentDegrees := protected.Group("/student-degrees")
		{
			// List all student degrees with pagination and filters
			studentDegrees.GET("", transcriptHandler.ListStudentDegrees)

			// Create student degree (temporary: no auth for testing)
			studentDegrees.POST("", transcriptHandler.CreateStudentDegree)

			// Get student degrees
			studentDegrees.GET("/user/:user_id", transcriptHandler.GetStudentDegrees)

			// Update degree status (admin only)
			studentDegrees.PUT("/:student_degree_id/status", RoleMiddleware(authClient, "admin"), transcriptHandler.UpdateDegreeStatus)
		}

		// Temporary: Public endpoint for testing student degree creation
		router.POST("/test-student-degrees", transcriptHandler.CreateStudentDegree)
	}
}
