package course

import (
	"context"
	"testing"
	"time"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	coursepb "github.com/olzzhas/edunite-server/course_service/pb/course"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockCourseRepository is a mock implementation of CourseRepository
type MockCourseRepository struct {
	mock.Mock
}

func (m *MockCourseRepository) CreateCourse(ctx context.Context, course *database.Course) error {
	args := m.Called(ctx, course)
	return args.Error(0)
}

func (m *MockCourseRepository) GetCourse(ctx context.Context, id int64) (*database.Course, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*database.Course), args.Error(1)
}

func (m *MockCourseRepository) GetAllCourses(ctx context.Context) ([]*database.Course, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*database.Course), args.Error(1)
}

func (m *MockCourseRepository) UpdateCourse(ctx context.Context, course *database.Course) error {
	args := m.Called(ctx, course)
	return args.Error(0)
}

func (m *MockCourseRepository) DeleteCourse(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockCourseRepository) AddPrerequisite(ctx context.Context, courseID, prerequisiteCourseID int64) error {
	args := m.Called(ctx, courseID, prerequisiteCourseID)
	return args.Error(0)
}

func (m *MockCourseRepository) GetPrerequisites(ctx context.Context, courseID int64) ([]int64, error) {
	args := m.Called(ctx, courseID)
	return args.Get(0).([]int64), args.Error(1)
}

func (m *MockCourseRepository) CheckPrerequisites(ctx context.Context, courseID, userID int64) (bool, error) {
	args := m.Called(ctx, courseID, userID)
	return args.Bool(0), args.Error(1)
}

func (m *MockCourseRepository) AddCourseToDegree(ctx context.Context, degreeCourse *database.DegreeCourse) error {
	args := m.Called(ctx, degreeCourse)
	if args.Error(0) == nil {
		// Simulate database setting ID and timestamps
		degreeCourse.ID = 1
		degreeCourse.CreatedAt = time.Now()
		degreeCourse.UpdatedAt = time.Now()
	}
	return args.Error(0)
}

func (m *MockCourseRepository) RemoveCourseFromDegree(ctx context.Context, degreeID, courseID int64) error {
	args := m.Called(ctx, degreeID, courseID)
	return args.Error(0)
}

func (m *MockCourseRepository) GetCoursesByDegree(ctx context.Context, degreeID int64) ([]*database.Course, error) {
	args := m.Called(ctx, degreeID)
	return args.Get(0).([]*database.Course), args.Error(1)
}

func (m *MockCourseRepository) GetDegreesByCourse(ctx context.Context, courseID int64) ([]int64, error) {
	args := m.Called(ctx, courseID)
	return args.Get(0).([]int64), args.Error(1)
}

func (m *MockCourseRepository) GetCoursesForStudentDegree(ctx context.Context, userID int64) ([]*database.Course, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*database.Course), args.Error(1)
}

func (m *MockCourseRepository) CheckCourseAccessForStudent(ctx context.Context, userID, courseID int64) (bool, error) {
	args := m.Called(ctx, userID, courseID)
	return args.Bool(0), args.Error(1)
}

func TestAddCourseToDegree(t *testing.T) {
	mockRepo := new(MockCourseRepository)
	service := NewCourseService(mockRepo)

	ctx := context.Background()
	req := &coursepb.AddCourseToDegreeRequest{
		DegreeId:       1,
		CourseId:       2,
		IsRequired:     true,
		SemesterNumber: 1,
	}

	mockRepo.On("AddCourseToDegree", ctx, mock.AnythingOfType("*database.DegreeCourse")).Return(nil)

	resp, err := service.AddCourseToDegree(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int64(1), resp.DegreeId)
	assert.Equal(t, int64(2), resp.CourseId)
	assert.True(t, resp.IsRequired)
	assert.Equal(t, int32(1), resp.SemesterNumber)
	mockRepo.AssertExpectations(t)
}

func TestAddCourseToDegree_ValidationError(t *testing.T) {
	mockRepo := new(MockCourseRepository)
	service := NewCourseService(mockRepo)

	ctx := context.Background()
	req := &coursepb.AddCourseToDegreeRequest{
		DegreeId: 0, // Invalid degree ID
		CourseId: 2,
	}

	resp, err := service.AddCourseToDegree(ctx, req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "validation failed")
}

func TestRemoveCourseFromDegree(t *testing.T) {
	mockRepo := new(MockCourseRepository)
	service := NewCourseService(mockRepo)

	ctx := context.Background()
	req := &coursepb.RemoveCourseFromDegreeRequest{
		DegreeId: 1,
		CourseId: 2,
	}

	mockRepo.On("RemoveCourseFromDegree", ctx, int64(1), int64(2)).Return(nil)

	resp, err := service.RemoveCourseFromDegree(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	mockRepo.AssertExpectations(t)
}

func TestGetCoursesByDegree(t *testing.T) {
	mockRepo := new(MockCourseRepository)
	service := NewCourseService(mockRepo)

	ctx := context.Background()
	req := &coursepb.GetCoursesByDegreeRequest{
		DegreeId: 1,
	}

	expectedCourses := []*database.Course{
		{
			ID:          1,
			Title:       "Test Course 1",
			Description: "Test Description 1",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          2,
			Title:       "Test Course 2",
			Description: "Test Description 2",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	mockRepo.On("GetCoursesByDegree", ctx, int64(1)).Return(expectedCourses, nil)

	resp, err := service.GetCoursesByDegree(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Courses, 2)
	assert.Equal(t, "Test Course 1", resp.Courses[0].Title)
	assert.Equal(t, "Test Course 2", resp.Courses[1].Title)
	mockRepo.AssertExpectations(t)
}

func TestGetCoursesForStudent(t *testing.T) {
	mockRepo := new(MockCourseRepository)
	service := NewCourseService(mockRepo)

	ctx := context.Background()
	req := &coursepb.GetCoursesForStudentRequest{
		UserId: 1,
	}

	expectedCourses := []*database.Course{
		{
			ID:          1,
			Title:       "Available Course",
			Description: "Course available to student",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	mockRepo.On("GetCoursesForStudentDegree", ctx, int64(1)).Return(expectedCourses, nil)

	resp, err := service.GetCoursesForStudent(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Courses, 1)
	assert.Equal(t, "Available Course", resp.Courses[0].Title)
	mockRepo.AssertExpectations(t)
}

func TestGetCoursesForStudent_ValidationError(t *testing.T) {
	mockRepo := new(MockCourseRepository)
	service := NewCourseService(mockRepo)

	ctx := context.Background()
	req := &coursepb.GetCoursesForStudentRequest{
		UserId: 0, // Invalid user ID
	}

	resp, err := service.GetCoursesForStudent(ctx, req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "validation failed")
}
