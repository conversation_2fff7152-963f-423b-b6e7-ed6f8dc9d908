package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	coursepb "github.com/olzzhas/edunite-server/course_service/pb/course"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// MockCourseServiceClient is a mock implementation of CourseServiceClient
type MockCourseServiceClient struct {
	mock.Mock
}

func (m *MockCourseServiceClient) CreateCourse(ctx context.Context, in *coursepb.CreateCourseRequest, opts ...grpc.CallOption) (*coursepb.CourseResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*coursepb.CourseResponse), args.Error(1)
}

func (m *MockCourseServiceClient) GetCourseByID(ctx context.Context, in *coursepb.GetCourseByIDRequest, opts ...grpc.CallOption) (*coursepb.CourseResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*coursepb.CourseResponse), args.Error(1)
}

func (m *MockCourseServiceClient) GetAllCourses(ctx context.Context, in *coursepb.EmptyRequest, opts ...grpc.CallOption) (*coursepb.CoursesResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*coursepb.CoursesResponse), args.Error(1)
}

func (m *MockCourseServiceClient) UpdateCourse(ctx context.Context, in *coursepb.UpdateCourseByIDRequest, opts ...grpc.CallOption) (*coursepb.CourseResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*coursepb.CourseResponse), args.Error(1)
}

func (m *MockCourseServiceClient) DeleteCourse(ctx context.Context, in *coursepb.DeleteCourseByIDRequest, opts ...grpc.CallOption) (*coursepb.EmptyResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*coursepb.EmptyResponse), args.Error(1)
}

func (m *MockCourseServiceClient) AddCourseToDegree(ctx context.Context, in *coursepb.AddCourseToDegreeRequest, opts ...grpc.CallOption) (*coursepb.DegreeCourseResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*coursepb.DegreeCourseResponse), args.Error(1)
}

func (m *MockCourseServiceClient) RemoveCourseFromDegree(ctx context.Context, in *coursepb.RemoveCourseFromDegreeRequest, opts ...grpc.CallOption) (*coursepb.EmptyResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*coursepb.EmptyResponse), args.Error(1)
}

func (m *MockCourseServiceClient) GetCoursesByDegree(ctx context.Context, in *coursepb.GetCoursesByDegreeRequest, opts ...grpc.CallOption) (*coursepb.CoursesResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*coursepb.CoursesResponse), args.Error(1)
}

func (m *MockCourseServiceClient) GetCoursesForStudent(ctx context.Context, in *coursepb.GetCoursesForStudentRequest, opts ...grpc.CallOption) (*coursepb.CoursesResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*coursepb.CoursesResponse), args.Error(1)
}

func TestAddCourseToDegree(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockClient := new(MockCourseServiceClient)
	handler := NewDegreeCourseHandler(mockClient)

	// Setup expected response
	expectedResponse := &coursepb.DegreeCourseResponse{
		Id:         1,
		DegreeId:   2,
		CourseId:   3,
		IsRequired: true,
		CreatedAt:  timestamppb.New(time.Now()),
		UpdatedAt:  timestamppb.New(time.Now()),
	}

	mockClient.On("AddCourseToDegree", mock.Anything, mock.AnythingOfType("*coursepb.AddCourseToDegreeRequest")).Return(expectedResponse, nil)

	// Setup request
	requestBody := map[string]interface{}{
		"degree_id":   2,
		"course_id":   3,
		"is_required": true,
	}
	jsonBody, _ := json.Marshal(requestBody)

	// Create request
	req, _ := http.NewRequest("POST", "/degree-courses", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Setup Gin context
	router := gin.New()
	router.POST("/degree-courses", handler.AddCourseToDegree)

	// Perform request
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, float64(1), response["id"])
	assert.Equal(t, float64(2), response["degree_id"])
	assert.Equal(t, float64(3), response["course_id"])
	assert.Equal(t, true, response["is_required"])

	mockClient.AssertExpectations(t)
}

func TestAddCourseToDegree_InvalidJSON(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockClient := new(MockCourseServiceClient)
	handler := NewDegreeCourseHandler(mockClient)

	// Create request with invalid JSON
	req, _ := http.NewRequest("POST", "/degree-courses", bytes.NewBuffer([]byte("invalid json")))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Setup Gin context
	router := gin.New()
	router.POST("/degree-courses", handler.AddCourseToDegree)

	// Perform request
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestRemoveCourseFromDegree(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockClient := new(MockCourseServiceClient)
	handler := NewDegreeCourseHandler(mockClient)

	// Setup expected response
	expectedResponse := &coursepb.EmptyResponse{}

	mockClient.On("RemoveCourseFromDegree", mock.Anything, mock.AnythingOfType("*coursepb.RemoveCourseFromDegreeRequest")).Return(expectedResponse, nil)

	// Create request
	req, _ := http.NewRequest("DELETE", "/degree-courses/degree/2/course/3", nil)

	// Create response recorder
	w := httptest.NewRecorder()

	// Setup Gin context
	router := gin.New()
	router.DELETE("/degree-courses/degree/:degree_id/course/:course_id", handler.RemoveCourseFromDegree)

	// Perform request
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Course removed from degree successfully", response["message"])

	mockClient.AssertExpectations(t)
}

func TestGetCoursesByDegree(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockClient := new(MockCourseServiceClient)
	handler := NewDegreeCourseHandler(mockClient)

	// Setup expected response
	expectedResponse := &coursepb.CoursesResponse{
		Courses: []*coursepb.CourseResponse{
			{
				Id:          1,
				Title:       "Test Course",
				Description: "Test Description",
				CreatedAt:   timestamppb.New(time.Now()),
				UpdatedAt:   timestamppb.New(time.Now()),
			},
		},
	}

	mockClient.On("GetCoursesByDegree", mock.Anything, mock.AnythingOfType("*coursepb.GetCoursesByDegreeRequest")).Return(expectedResponse, nil)

	// Create request
	req, _ := http.NewRequest("GET", "/degree-courses/degree/2/courses", nil)

	// Create response recorder
	w := httptest.NewRecorder()

	// Setup Gin context
	router := gin.New()
	router.GET("/degree-courses/degree/:degree_id/courses", handler.GetCoursesByDegree)

	// Perform request
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	
	courses := response["courses"].([]interface{})
	assert.Len(t, courses, 1)
	
	course := courses[0].(map[string]interface{})
	assert.Equal(t, float64(1), course["id"])
	assert.Equal(t, "Test Course", course["title"])

	mockClient.AssertExpectations(t)
}

func TestGetCoursesForStudent(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockClient := new(MockCourseServiceClient)
	handler := NewDegreeCourseHandler(mockClient)

	// Setup expected response
	expectedResponse := &coursepb.CoursesResponse{
		Courses: []*coursepb.CourseResponse{
			{
				Id:          1,
				Title:       "Available Course",
				Description: "Course available to student",
				CreatedAt:   timestamppb.New(time.Now()),
				UpdatedAt:   timestamppb.New(time.Now()),
			},
		},
	}

	mockClient.On("GetCoursesForStudent", mock.Anything, mock.AnythingOfType("*coursepb.GetCoursesForStudentRequest")).Return(expectedResponse, nil)

	// Create request
	req, _ := http.NewRequest("GET", "/students/1/available-courses", nil)

	// Create response recorder
	w := httptest.NewRecorder()

	// Setup Gin context
	router := gin.New()
	router.GET("/students/:user_id/available-courses", handler.GetCoursesForStudent)

	// Perform request
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	
	courses := response["courses"].([]interface{})
	assert.Len(t, courses, 1)
	
	course := courses[0].(map[string]interface{})
	assert.Equal(t, float64(1), course["id"])
	assert.Equal(t, "Available Course", course["title"])

	mockClient.AssertExpectations(t)
}
