package database

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
)

// Course структура курса
type Course struct {
	ID             int64     `json:"id"`
	Title          string    `json:"title"`
	Description    string    `json:"description"`
	BannerImageUrl string    `json:"banner_image_url"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// DegreeCourse структура связи курса с программой
type DegreeCourse struct {
	ID             int64     `json:"id"`
	DegreeID       int64     `json:"degree_id"`
	CourseID       int64     `json:"course_id"`
	IsRequired     bool      `json:"is_required"`
	SemesterNumber *int      `json:"semester_number,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// CourseRepository интерфейс для работы с таблицей courses
type CourseRepository interface {
	CreateCourse(ctx context.Context, course *Course) error
	GetCourse(ctx context.Context, id int64) (*Course, error)
	GetAllCourses(ctx context.Context) ([]*Course, error)
	UpdateCourse(ctx context.Context, course *Course) error
	DeleteCourse(ctx context.Context, id int64) error
	AddPrerequisite(ctx context.Context, courseID, prerequisiteCourseID int64) error
	GetPrerequisites(ctx context.Context, courseID int64) ([]int64, error)
	CheckPrerequisites(ctx context.Context, courseID, userID int64) (bool, error)

	// Degree-Course relationship methods
	AddCourseToDegree(ctx context.Context, degreeCourse *DegreeCourse) error
	RemoveCourseFromDegree(ctx context.Context, degreeID, courseID int64) error
	GetCoursesByDegree(ctx context.Context, degreeID int64) ([]*Course, error)
	GetDegreesByCourse(ctx context.Context, courseID int64) ([]int64, error)
	GetCoursesForStudentDegree(ctx context.Context, userID int64) ([]*Course, error)
	CheckCourseAccessForStudent(ctx context.Context, userID, courseID int64) (bool, error)
}

type courseRepository struct {
	db *pgxpool.Pool
}

// NewCourseRepository создает новый экземпляр courseRepository
func NewCourseRepository(db *pgxpool.Pool) CourseRepository {
	return &courseRepository{db: db}
}

func ValidateCourse(v *validator.Validator, course *Course) {
	v.Check(course.Title != "", "title", "must be provided")
	v.Check(len(course.Title) <= 255, "title", "must not exceed 255 characters")

	v.Check(len(course.Description) <= 2000, "description", "must not exceed 2000 characters")
}

func (c *courseRepository) CreateCourse(ctx context.Context, course *Course) error {
	query := `
		INSERT INTO courses (title, description, banner_image_url)
		VALUES ($1, $2, $3)
		RETURNING id, created_at, updated_at
	`
	row := c.db.QueryRow(ctx, query, course.Title, course.Description, course.BannerImageUrl)

	err := row.Scan(&course.ID, &course.CreatedAt, &course.UpdatedAt)
	if err != nil {
		return err
	}

	return nil
}

func (c *courseRepository) GetCourse(ctx context.Context, id int64) (*Course, error) {
	query := `
		SELECT id, title, description, banner_image_url, created_at, updated_at
		FROM courses
		WHERE id = $1
	`
	row := c.db.QueryRow(ctx, query, id)

	var course Course
	err := row.Scan(
		&course.ID,
		&course.Title,
		&course.Description,
		&course.BannerImageUrl,
		&course.CreatedAt,
		&course.UpdatedAt,
	)

	if err != nil {
		// err может быть pgx.ErrNoRows, если запись не найдена.
		return nil, err
	}

	return &course, nil
}

func (c *courseRepository) GetAllCourses(ctx context.Context) ([]*Course, error) {
	query := `
		SELECT id, title, description, banner_image_url, created_at, updated_at
		FROM courses
		ORDER BY id
	`
	rows, err := c.db.Query(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var courses []*Course
	for rows.Next() {
		var course Course
		if err := rows.Scan(
			&course.ID,
			&course.Title,
			&course.Description,
			&course.BannerImageUrl,
			&course.CreatedAt,
			&course.UpdatedAt,
		); err != nil {
			return nil, err
		}
		courses = append(courses, &course)
	}

	if rows.Err() != nil {
		return nil, rows.Err()
	}

	return courses, nil
}

func (c *courseRepository) UpdateCourse(ctx context.Context, course *Course) error {
	query := `
		UPDATE courses
		SET title = $1,
			description = $2,
			banner_image_url = $3,
			updated_at = NOW()
		WHERE id = $4
		RETURNING updated_at
	`
	row := c.db.QueryRow(ctx, query,
		course.Title,
		course.Description,
		course.BannerImageUrl,
		course.ID,
	)

	err := row.Scan(&course.UpdatedAt)
	if err != nil {
		return err
	}

	return nil
}

func (c *courseRepository) DeleteCourse(ctx context.Context, id int64) error {
	query := `
		DELETE FROM courses
		WHERE id = $1
	`
	cmdTag, err := c.db.Exec(ctx, query, id)
	if err != nil {
		return err
	}
	if cmdTag.RowsAffected() == 0 {
		return errors.New("service not found")
	}

	return nil
}

// AddPrerequisite adds a prerequisite course for a given course.
func (c *courseRepository) AddPrerequisite(ctx context.Context, courseID, prerequisiteCourseID int64) error {
	query := `
		INSERT INTO prerequisites (course_id, prerequisite_course_id)
		VALUES ($1, $2)
	`
	_, err := c.db.Exec(ctx, query, courseID, prerequisiteCourseID)
	return err
}

// GetPrerequisites retrieves all prerequisite courses for a given course.
func (c *courseRepository) GetPrerequisites(ctx context.Context, courseID int64) ([]int64, error) {
	query := `
		SELECT prerequisite_course_id
		FROM prerequisites
		WHERE course_id = $1
	`
	rows, err := c.db.Query(ctx, query, courseID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var prerequisiteIDs []int64
	for rows.Next() {
		var prerequisiteID int64
		if err := rows.Scan(&prerequisiteID); err != nil {
			return nil, err
		}
		prerequisiteIDs = append(prerequisiteIDs, prerequisiteID)
	}
	return prerequisiteIDs, rows.Err()
}

// CheckPrerequisites checks if a student has completed all prerequisites with a final grade between 50 and 100.
func (c *courseRepository) CheckPrerequisites(ctx context.Context, courseID, userID int64) (bool, error) {
	query := `
		SELECT COUNT(*) = COUNT(CASE WHEN tr.final_grade >= 50 AND tr.final_grade <= 100 THEN 1 END)
		FROM prerequisites p
		JOIN thread_registrations tr ON tr.thread_id IN (SELECT id FROM threads WHERE course_id = p.prerequisite_course_id)
		WHERE p.course_id = $1 AND tr.user_id = $2
	`
	var allPrerequisitesMet bool
	err := c.db.QueryRow(ctx, query, courseID, userID).Scan(&allPrerequisitesMet)
	return allPrerequisitesMet, err
}

// AddCourseToDegree добавляет курс к программе
func (c *courseRepository) AddCourseToDegree(ctx context.Context, degreeCourse *DegreeCourse) error {
	query := `
		INSERT INTO degree_courses (degree_id, course_id, is_required, semester_number)
		VALUES ($1, $2, $3, $4)
		RETURNING id, created_at, updated_at
	`
	row := c.db.QueryRow(ctx, query,
		degreeCourse.DegreeID,
		degreeCourse.CourseID,
		degreeCourse.IsRequired,
		degreeCourse.SemesterNumber)

	return row.Scan(&degreeCourse.ID, &degreeCourse.CreatedAt, &degreeCourse.UpdatedAt)
}

// RemoveCourseFromDegree удаляет курс из программы
func (c *courseRepository) RemoveCourseFromDegree(ctx context.Context, degreeID, courseID int64) error {
	query := `DELETE FROM degree_courses WHERE degree_id = $1 AND course_id = $2`
	_, err := c.db.Exec(ctx, query, degreeID, courseID)
	return err
}

// GetCoursesByDegree возвращает все курсы для определенной программы
func (c *courseRepository) GetCoursesByDegree(ctx context.Context, degreeID int64) ([]*Course, error) {
	query := `
		SELECT c.id, c.title, c.description, c.banner_image_url, c.created_at, c.updated_at
		FROM courses c
		INNER JOIN degree_courses dc ON c.id = dc.course_id
		WHERE dc.degree_id = $1
		ORDER BY dc.semester_number, c.title
	`
	rows, err := c.db.Query(ctx, query, degreeID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var courses []*Course
	for rows.Next() {
		course := &Course{}
		err := rows.Scan(
			&course.ID,
			&course.Title,
			&course.Description,
			&course.BannerImageUrl,
			&course.CreatedAt,
			&course.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		courses = append(courses, course)
	}
	return courses, rows.Err()
}

// GetDegreesByCourse возвращает все программы, к которым привязан курс
func (c *courseRepository) GetDegreesByCourse(ctx context.Context, courseID int64) ([]int64, error) {
	query := `
		SELECT degree_id
		FROM degree_courses
		WHERE course_id = $1
	`
	rows, err := c.db.Query(ctx, query, courseID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var degreeIDs []int64
	for rows.Next() {
		var degreeID int64
		if err := rows.Scan(&degreeID); err != nil {
			return nil, err
		}
		degreeIDs = append(degreeIDs, degreeID)
	}
	return degreeIDs, rows.Err()
}

// GetCoursesForStudentDegree возвращает все курсы, доступные студенту на основе его программы
func (c *courseRepository) GetCoursesForStudentDegree(ctx context.Context, userID int64) ([]*Course, error) {
	query := `
		SELECT DISTINCT c.id, c.title, c.description, c.banner_image_url, c.created_at, c.updated_at
		FROM courses c
		INNER JOIN degree_courses dc ON c.id = dc.course_id
		INNER JOIN student_degrees sd ON dc.degree_id = sd.degree_id
		WHERE sd.user_id = $1 AND sd.status = 'in_progress'
		ORDER BY dc.semester_number, c.title
	`
	rows, err := c.db.Query(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var courses []*Course
	for rows.Next() {
		course := &Course{}
		err := rows.Scan(
			&course.ID,
			&course.Title,
			&course.Description,
			&course.BannerImageUrl,
			&course.CreatedAt,
			&course.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		courses = append(courses, course)
	}
	return courses, rows.Err()
}

// CheckCourseAccessForStudent проверяет, имеет ли студент доступ к курсу на основе его программы
func (c *courseRepository) CheckCourseAccessForStudent(ctx context.Context, userID, courseID int64) (bool, error) {
	query := `
		SELECT EXISTS(
			SELECT 1
			FROM degree_courses dc
			INNER JOIN student_degrees sd ON dc.degree_id = sd.degree_id
			WHERE sd.user_id = $1 AND dc.course_id = $2 AND sd.status = 'in_progress'
		)
	`
	var hasAccess bool
	err := c.db.QueryRow(ctx, query, userID, courseID).Scan(&hasAccess)
	return hasAccess, err
}
