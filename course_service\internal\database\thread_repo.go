package database

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgconn"
	"github.com/jackc/pgx/v4"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"

	"github.com/jackc/pgx/v4/pgxpool"
)

var (
	ErrRegistrationExists   = errors.New("registration already exists")
	ErrRegistrationNotFound = errors.New("registration not found")
	ErrThreadFull           = errors.New("thread is full")
	ErrAlreadyRegistered    = errors.New("user already registered")
)

// Thread модель таблицы threads
type Thread struct {
	ID          int64     `json:"id"`
	CourseID    int64     `json:"course_id"`
	SemesterID  int64     `json:"semester_id"`
	TeacherID   int64     `json:"teacher_id"`
	MaxStudents int32     `json:"max_students"`
	Title       string    `json:"title"`
	SyllabusURL string    `json:"syllabus_url"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ThreadRegistration модель таблицы thread_registrations
type ThreadRegistration struct {
	UserID           int64     `json:"user_id"`
	ThreadID         int64     `json:"thread_id"`
	RegistrationDate time.Time `json:"registration_date"`
	FinalGrade       float64   `json:"final_grade"`
}

// ThreadRepository интерфейс CRUD для threads
type ThreadRepository interface {
	CreateThread(ctx context.Context, thread *Thread) error
	GetThread(ctx context.Context, id int64) (*Thread, error)
	GetThreadsByCourse(ctx context.Context, courseID int64) ([]*Thread, error)
	GetAllThreads(ctx context.Context) ([]*Thread, error)
	UpdateThread(ctx context.Context, thread *Thread) error
	DeleteThread(ctx context.Context, id int64) error

	RegisterUserToThread(ctx context.Context, userID, threadID int64) error
	RegisterManyUsersToThread(ctx context.Context, threadID int64, userIDs []int64) error
	RemoveRegistrationToThread(ctx context.Context, userID, threadID int64) error
	RemoveManyRegistrationsToThread(ctx context.Context, threadID int64, userIDs []int64) error
	ListThreadRegistrations(ctx context.Context, threadID int64) ([]*ThreadRegistration, error)
	UpdateFinalGrade(ctx context.Context, userID, threadID int64, finalGrade float64) error
}

type threadRepository struct {
	db *pgxpool.Pool
}

func ValidateThread(v *validator.Validator, t *Thread) {
	v.Check(t.CourseID > 0, "course_id", "must be provided and > 0")
	v.Check(t.SemesterID > 0, "semester_id", "must be provided and > 0")
	v.Check(t.TeacherID > 0, "teacher_id", "must be provided and > 0")
	v.Check(t.MaxStudents > 0, "max_students", "must be provided and > 0")
	v.Check(t.Title != "", "title", "must be provided")
	v.Check(len(t.Title) <= 255, "title", "max length is 255 characters")
	if t.SyllabusURL != "" {
		v.Check(len(t.SyllabusURL) <= 500, "syllabus_url", "max length is 500 characters")
	}
}

func (r *threadRepository) ListThreadRegistrations(ctx context.Context, threadID int64) ([]*ThreadRegistration, error) {
	query := `
        SELECT user_id, thread_id, registration_date
        FROM thread_registrations
        WHERE thread_id = $1
        ORDER BY registration_date DESC
    `

	rows, err := r.db.Query(ctx, query, threadID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var registrations []*ThreadRegistration
	for rows.Next() {
		var reg ThreadRegistration
		if err := rows.Scan(&reg.UserID, &reg.ThreadID, &reg.RegistrationDate); err != nil {
			return nil, err
		}
		registrations = append(registrations, &reg)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return registrations, nil
}

// NewThreadRepository конструктор репозитория Thread
func NewThreadRepository(db *pgxpool.Pool) ThreadRepository {
	return &threadRepository{db: db}
}

// CreateThread вставка новой записи в таблицу threads
func (r *threadRepository) CreateThread(ctx context.Context, thread *Thread) error {
	query := `
        INSERT INTO threads (course_id, semester_id, teacher_id, max_students, title, syllabus_url)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, created_at, updated_at
    `
	row := r.db.QueryRow(ctx, query,
		thread.CourseID,
		thread.SemesterID,
		thread.TeacherID,
		thread.MaxStudents,
		thread.Title,
		thread.SyllabusURL,
	)

	if err := row.Scan(&thread.ID, &thread.CreatedAt, &thread.UpdatedAt); err != nil {
		return err
	}
	return nil
}

// GetThread получение одной записи по ID
func (r *threadRepository) GetThread(ctx context.Context, id int64) (*Thread, error) {
	query := `
        SELECT id, course_id, semester_id, teacher_id, max_students, title, syllabus_url, created_at, updated_at
        FROM threads
        WHERE id = $1
    `
	row := r.db.QueryRow(ctx, query, id)

	var t Thread
	if err := row.Scan(
		&t.ID,
		&t.CourseID,
		&t.SemesterID,
		&t.TeacherID,
		&t.MaxStudents,
		&t.Title,
		&t.SyllabusURL,
		&t.CreatedAt,
		&t.UpdatedAt,
	); err != nil {
		return nil, err
	}
	return &t, nil
}

// GetThreadsByCourse выборка всех потоков по course_id
func (r *threadRepository) GetThreadsByCourse(ctx context.Context, courseID int64) ([]*Thread, error) {
	query := `
        SELECT id, course_id, semester_id, teacher_id, max_students, title, syllabus_url, created_at, updated_at
        FROM threads
        WHERE course_id = $1
        ORDER BY id
    `
	rows, err := r.db.Query(ctx, query, courseID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var threads []*Thread
	for rows.Next() {
		var t Thread
		if err := rows.Scan(
			&t.ID,
			&t.CourseID,
			&t.SemesterID,
			&t.TeacherID,
			&t.MaxStudents,
			&t.Title,
			&t.SyllabusURL,
			&t.CreatedAt,
			&t.UpdatedAt,
		); err != nil {
			return nil, err
		}
		threads = append(threads, &t)
	}
	return threads, rows.Err()
}

// GetAllThreads выборка всех потоков
func (r *threadRepository) GetAllThreads(ctx context.Context) ([]*Thread, error) {
	query := `
        SELECT id, course_id, semester_id, teacher_id, max_students, title, syllabus_url, created_at, updated_at
        FROM threads
        ORDER BY id
    `
	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var threads []*Thread
	for rows.Next() {
		var t Thread
		if err := rows.Scan(
			&t.ID,
			&t.CourseID,
			&t.SemesterID,
			&t.TeacherID,
			&t.MaxStudents,
			&t.Title,
			&t.SyllabusURL,
			&t.CreatedAt,
			&t.UpdatedAt,
		); err != nil {
			return nil, err
		}
		threads = append(threads, &t)
	}
	return threads, rows.Err()
}

// UpdateThread обновление записи в таблице threads
func (r *threadRepository) UpdateThread(ctx context.Context, thread *Thread) error {
	query := `
        UPDATE threads
        SET course_id    = $1,
            semester_id  = $2,
            teacher_id   = $3,
            max_students = $4,
            title        = $5,
            syllabus_url = $6,
            updated_at   = NOW()
        WHERE id = $7
        RETURNING updated_at
    `
	row := r.db.QueryRow(ctx, query,
		thread.CourseID,
		thread.SemesterID,
		thread.TeacherID,
		thread.MaxStudents,
		thread.Title,
		thread.SyllabusURL,
		thread.ID,
	)

	if err := row.Scan(&thread.UpdatedAt); err != nil {
		return err
	}
	return nil
}

// DeleteThread удаление записи из таблицы threads
func (r *threadRepository) DeleteThread(ctx context.Context, id int64) error {
	query := `
        DELETE FROM threads
        WHERE id = $1
    `
	cmdTag, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return err
	}
	if cmdTag.RowsAffected() == 0 {
		return errors.New("thread not found")
	}
	return nil
}

// -------------------------------------------
// Методы для thread_registrations
// -------------------------------------------

func (r *threadRepository) RegisterUserToThread(ctx context.Context, userID, threadID int64) error {
	// 1) Начинаем транзакцию
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return err
	}
	defer tx.Rollback(ctx)

	// 2) Блокируем строку потока, чтобы другие одновременно не прошли проверку
	var capacity int32
	row := tx.QueryRow(ctx,
		`SELECT max_students
           FROM threads
          WHERE id = $1
          FOR UPDATE`, threadID)
	if err := row.Scan(&capacity); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New("thread not found")
		}
		return err
	}

	// 3) Считаем текущее число регистраций
	var count int64
	if err := tx.QueryRow(ctx,
		`SELECT COUNT(*) FROM thread_registrations WHERE thread_id = $1`,
		threadID,
	).Scan(&count); err != nil {
		return err
	}

	// 4) Проверяем, что ещё есть свободные места
	if count >= int64(capacity) {
		return ErrThreadFull
	}

	// 5) Пытаемся добавить регистрацию
	//    допускаем duplicate key, но обрабатываем как ошибку
	cmd, err := tx.Exec(ctx,
		`INSERT INTO thread_registrations(user_id, thread_id)
             VALUES($1, $2)`,
		userID, threadID,
	)
	if err != nil {
		// если уже есть такая запись
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == "23505" {
			return ErrAlreadyRegistered
		}
		return err
	}
	if cmd.RowsAffected() == 0 {
		return ErrAlreadyRegistered
	}

	// 6) Фиксим транзакцию
	return tx.Commit(ctx)
}

// RegisterManyUsersToThread регистрирует нескольких пользователей в потоке
func (r *threadRepository) RegisterManyUsersToThread(ctx context.Context, threadID int64, userIDs []int64) error {
	if len(userIDs) == 0 {
		return errors.New("no user_ids provided")
	}

	// Формируем VALUES часть запроса
	values := ""
	args := []interface{}{threadID}
	for i, userID := range userIDs {
		values += fmt.Sprintf("($1, $%d),", i+2)
		args = append(args, userID)
	}
	values = values[:len(values)-1] // Убираем последнюю запятую

	query := fmt.Sprintf(`
        INSERT INTO thread_registrations (thread_id, user_id)
        VALUES %s
        ON CONFLICT (thread_id, user_id) DO NOTHING
    `, values)

	cmdTag, err := r.db.Exec(ctx, query, args...)
	if err != nil {
		return err
	}

	if cmdTag.RowsAffected() == 0 {
		return errors.New("all registrations already exist")
	}

	return nil
}

// RemoveRegistrationToThread удаляет регистрацию пользователя из потока
func (r *threadRepository) RemoveRegistrationToThread(ctx context.Context, userID, threadID int64) error {
	query := `
        DELETE FROM thread_registrations
        WHERE user_id = $1 AND thread_id = $2
    `
	cmdTag, err := r.db.Exec(ctx, query, userID, threadID)
	if err != nil {
		return err
	}
	if cmdTag.RowsAffected() == 0 {
		return ErrRegistrationNotFound
	}
	return nil
}

// RemoveManyRegistrationsToThread удаляет регистрации нескольких пользователей из потока
func (r *threadRepository) RemoveManyRegistrationsToThread(ctx context.Context, threadID int64, userIDs []int64) error {
	if len(userIDs) == 0 {
		return errors.New("no user_ids provided")
	}

	query := `
        DELETE FROM thread_registrations
        WHERE thread_id = $1 AND user_id = ANY($2)
    `
	cmdTag, err := r.db.Exec(ctx, query, threadID, userIDs)
	if err != nil {
		return err
	}

	if cmdTag.RowsAffected() == 0 {
		return errors.New("no registrations found to delete")
	}

	return nil
}

// UpdateFinalGrade updates the final grade for a thread registration
func (r *threadRepository) UpdateFinalGrade(ctx context.Context, userID, threadID int64, finalGrade float64) error {
	query := `
        UPDATE thread_registrations
        SET final_grade = $1
        WHERE user_id = $2 AND thread_id = $3
    `
	cmdTag, err := r.db.Exec(ctx, query, finalGrade, userID, threadID)
	if err != nil {
		return err
	}
	if cmdTag.RowsAffected() == 0 {
		return ErrRegistrationNotFound
	}
	return nil
}
