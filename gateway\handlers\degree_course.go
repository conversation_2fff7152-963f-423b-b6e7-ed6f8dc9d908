package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	coursepb "github.com/olzzhas/edunite-server/course_service/pb/course"
)

type DegreeCourseHandler struct {
	courseClient coursepb.CourseServiceClient
}

func NewDegreeCourseHandler(courseClient coursepb.CourseServiceClient) *DegreeCourseHandler {
	return &DegreeCourseHandler{
		courseClient: courseClient,
	}
}

// AddCourseToDegreeRequest represents the request to add a course to a degree
type AddCourseToDegreeRequest struct {
	DegreeID       int64 `json:"degree_id" binding:"required"`
	CourseID       int64 `json:"course_id" binding:"required"`
	IsRequired     bool  `json:"is_required"`
	SemesterNumber *int  `json:"semester_number,omitempty"`
}

// AddCourseToDegree добавляет курс к программе
func (h *DegreeCourseHandler) AddCourseToDegree(c *gin.Context) {
	var req AddCourseToDegreeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Convert to protobuf request
	pbReq := &coursepb.AddCourseToDegreeRequest{
		DegreeId:   req.DegreeID,
		CourseId:   req.CourseID,
		IsRequired: req.IsRequired,
	}
	if req.SemesterNumber != nil {
		pbReq.SemesterNumber = int32(*req.SemesterNumber)
	}

	resp, err := h.courseClient.AddCourseToDegree(c.Request.Context(), pbReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// RemoveCourseFromDegree удаляет курс из программы
func (h *DegreeCourseHandler) RemoveCourseFromDegree(c *gin.Context) {
	degreeIDStr := c.Param("degree_id")
	courseIDStr := c.Param("course_id")

	degreeID, err := strconv.ParseInt(degreeIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid degree_id"})
		return
	}

	courseID, err := strconv.ParseInt(courseIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid course_id"})
		return
	}

	pbReq := &coursepb.RemoveCourseFromDegreeRequest{
		DegreeId: degreeID,
		CourseId: courseID,
	}

	_, err = h.courseClient.RemoveCourseFromDegree(c.Request.Context(), pbReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Course removed from degree successfully"})
}

// GetCoursesByDegree возвращает все курсы для определенной программы
func (h *DegreeCourseHandler) GetCoursesByDegree(c *gin.Context) {
	degreeIDStr := c.Param("degree_id")
	degreeID, err := strconv.ParseInt(degreeIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid degree_id"})
		return
	}

	pbReq := &coursepb.GetCoursesByDegreeRequest{
		DegreeId: degreeID,
	}

	resp, err := h.courseClient.GetCoursesByDegree(c.Request.Context(), pbReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetCoursesForStudent возвращает курсы, доступные студенту на основе его программы
func (h *DegreeCourseHandler) GetCoursesForStudent(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user_id"})
		return
	}

	pbReq := &coursepb.GetCoursesForStudentRequest{
		UserId: userID,
	}

	resp, err := h.courseClient.GetCoursesForStudent(c.Request.Context(), pbReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}
